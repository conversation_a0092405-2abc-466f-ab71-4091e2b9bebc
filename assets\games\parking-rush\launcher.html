<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking Rush - Game Launcher</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .launcher-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .game-description {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: left;
        }
        .game-description h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: left;
        }
        .feature h4 {
            color: #FFD700;
            margin-bottom: 10px;
        }
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        .play-btn {
            background: #4CAF50;
            color: white;
        }
        .demo-btn {
            background: #2196F3;
            color: white;
        }
        .test-btn {
            background: #ff9800;
            color: white;
        }
        button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }
        .info {
            margin-top: 30px;
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <h1>🚗 Parking Rush</h1>
        <p style="font-size: 20px; margin-bottom: 30px;">
            The ultimate car parking puzzle game!
        </p>
        
        <div class="game-description">
            <h3>🎮 How to Play:</h3>
            <p>• Draw paths from colored cars to matching colored parking spots</p>
            <p>• Avoid car collisions while crossing paths</p>
            <p>• Complete all 3 levels with increasing difficulty</p>
            <p>• Use strategy and planning to solve parking puzzles</p>
        </div>

        <div class="features">
            <div class="feature">
                <h4>🎯 Game Features</h4>
                <p>• Touch/mouse drawing controls</p>
                <p>• Real-time collision detection</p>
                <p>• Smooth car animations</p>
                <p>• 3 challenging levels</p>
            </div>
            <div class="feature">
                <h4>📱 Mobile Ready</h4>
                <p>• Responsive design</p>
                <p>• Touch-friendly controls</p>
                <p>• Works on all devices</p>
                <p>• Full-screen support</p>
            </div>
        </div>
        
        <div class="buttons">
            <button class="play-btn" onclick="playGame()">
                🎮 Play Full Game
            </button>
            <button class="demo-btn" onclick="openDemo()">
                📺 View Demo
            </button>
            <button class="test-btn" onclick="openTest()">
                🧪 Test Page
            </button>
        </div>
        
        <div class="info">
            <p><strong>Parking Rush</strong> - Created for Easy Money App</p>
            <p>HTML5 Canvas game with touch controls and smooth animations</p>
            <p>Perfect for mobile devices and WebView integration</p>
        </div>
    </div>

    <script>
        function playGame() {
            window.open('standalone.html', '_blank');
        }

        function openDemo() {
            window.open('demo.html', '_blank');
        }

        function openTest() {
            window.open('test.html', '_blank');
        }

        // Auto-open game after 3 seconds if no interaction
        setTimeout(() => {
            if (confirm('🚗 Ready to play Parking Rush? Click OK to start!')) {
                playGame();
            }
        }, 3000);
    </script>
</body>
</html>
