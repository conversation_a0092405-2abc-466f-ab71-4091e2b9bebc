import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:io';
import '../services/appodeal_service.dart';
import '../services/global_ad_manager.dart';
import '../widgets/global_banner_ad.dart';

class GamesScreen extends StatefulWidget {
  const GamesScreen({super.key});

  @override
  State<GamesScreen> createState() => _GamesScreenState();
}

class _GamesScreenState extends State<GamesScreen> with TickerProviderStateMixin, GlobalAdMixin {
  late AnimationController _scaleController;
  final AppodealService _appodealService = AppodealService();

  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              babyBlue,
              Color(0xFFE8F4FD),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Header
                _buildModernHeader(),

                // All Games
                _buildAllGames(),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: const GlobalBannerAd(
        adId: 'Games Banner',
        height: 60,
      ),
    );
  }

  Widget _buildModernHeader() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🎮 Gaming Zone',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w900,
                        color: navyBlue,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Play games • Earn rewards • Have fun!',
                      style: TextStyle(
                        fontSize: 14,
                        color: navyBlue.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [goldenYellow, sunsetOrange],
                  ),
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: [
                    BoxShadow(
                      color: goldenYellow.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.leaderboard_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Stats Row
          Row(
            children: [
              Expanded(
                child: _buildStatCard('Games Won', '47', Icons.emoji_events_rounded, [coral, sunsetOrange]),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard('Total Earned', '₹284', Icons.monetization_on_rounded, [mintGreen, lavender]),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard('Streak', '5 days', Icons.local_fire_department_rounded, [goldenYellow, sunsetOrange]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, List<Color> colors) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: colors[0],
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: colors[0],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: navyBlue.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDailyMissions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🎯 Daily Missions',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [coral, sunsetOrange],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: coral.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '🔥 Today\'s Challenge',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '18h left',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildMissionItem('Win 5 Games', '₹25', '3/5', 0.6),
                const SizedBox(height: 12),
                _buildMissionItem('Play 10 Games', '₹15', '7/10', 0.7),
                const SizedBox(height: 12),
                _buildMissionItem('Complete Puzzle', '₹30', '0/1', 0.0),
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildMissionItem(String mission, String reward, String progress, double progressValue) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                mission,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                reward,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progressValue,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                progress,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGameCategories() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🎲 Game Categories',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildCategoryCard('Puzzle', '12 games', Icons.extension_rounded, [blueGrotto, blueGreen]),
                const SizedBox(width: 16),
                _buildCategoryCard('Action', '8 games', Icons.flash_on_rounded, [coral, sunsetOrange]),
                const SizedBox(width: 16),
                _buildCategoryCard('Strategy', '6 games', Icons.psychology_rounded, [mintGreen, lavender]),
                const SizedBox(width: 16),
                _buildCategoryCard('Casual', '15 games', Icons.sports_esports_rounded, [goldenYellow, sunsetOrange]),
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(String title, String count, IconData icon, List<Color> colors) {
    return Container(
      width: 140,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 32,
          ),
          const Spacer(),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedGames() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '⭐ Featured Games',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFeaturedGameCard(
                  'Puzzle Master',
                  'Brain-teasing challenges',
                  '₹25',
                  '4.8 ⭐',
                  [coral, sunsetOrange],
                ),
                const SizedBox(width: 16),
                _buildFeaturedGameCard(
                  'Memory Champion',
                  'Test your memory skills',
                  '₹20',
                  '4.9 ⭐',
                  [blueGrotto, blueGreen],
                ),
                const SizedBox(width: 16),
                _buildFeaturedGameCard(
                  'Quick Math',
                  'Fast calculation game',
                  '₹15',
                  '4.7 ⭐',
                  [mintGreen, lavender],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildFeaturedGameCard(String title, String description, String reward, String rating, List<Color> colors) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: colors),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.games_rounded,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: colors),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  reward,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: navyBlue.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                rating,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: colors[0],
                ),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors[0],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Play',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAllGames() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🎮 All Games',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 16),
          LayoutBuilder(
            builder: (context, constraints) {
              final crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;
              final childAspectRatio = constraints.maxWidth > 600 ? 0.9 : 0.75;

              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: childAspectRatio,
            children: [
              _buildModernGameCard('Parking Rush', 'Draw paths for cars', '₹15', Icons.local_parking_rounded, [coral, sunsetOrange], 'parking-rush', '🚗'),
              _buildModernGameCard('Memory Match', 'Test your memory', '₹8', Icons.psychology_rounded, [blueGrotto, blueGreen], 'memory-match', '🧠'),
              _buildModernGameCard('Word Puzzle', 'Find hidden words', '₹12', Icons.text_fields_rounded, [mintGreen, lavender], 'word-puzzle', '📝'),
              _buildModernGameCard('Number Game', 'Math challenges', '₹10', Icons.calculate_rounded, [goldenYellow, sunsetOrange], 'number-game', '🔢'),
              _buildModernGameCard('Color Match', 'Match colors fast', '₹6', Icons.palette_rounded, [lavender, roseGold], 'color-match', '🎨'),
              _buildModernGameCard('Brain Teaser', 'Logic puzzles', '₹15', Icons.lightbulb_rounded, [blueGrotto, mintGreen], 'brain-teaser', '💡'),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildModernGameCard(String title, String description, String reward, IconData icon, List<Color> colors, String? gameId, String thumbnail) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thumbnail Section
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: colors,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                // Background pattern
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.1),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
                // Thumbnail
                Center(
                  child: Text(
                    thumbnail,
                    style: const TextStyle(
                      fontSize: 40,
                    ),
                  ),
                ),
                // Reward badge
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      reward,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                        color: colors[0],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Content Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: navyBlue,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 11,
                      color: navyBlue.withOpacity(0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _playGame(gameId, title),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors[0],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Play Now',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _playGame(String? gameId, String title) {
    if (gameId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$title coming soon!'),
          backgroundColor: coral,
        ),
      );
      return;
    }

    if (gameId == 'parking-rush') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GameWebViewScreen(
            gameId: gameId,
            gameTitle: title,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$title coming soon!'),
          backgroundColor: coral,
        ),
      );
    }
  }
}

class GameWebViewScreen extends StatefulWidget {
  final String gameId;
  final String gameTitle;

  const GameWebViewScreen({
    super.key,
    required this.gameId,
    required this.gameTitle,
  });

  @override
  State<GameWebViewScreen> createState() => _GameWebViewScreenState();
}

class _GameWebViewScreenState extends State<GameWebViewScreen> {
  late final WebViewController controller;
  final AppodealService _appodealService = AppodealService();
  static const Color navyBlue = Color(0xFF05445E);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color mintGreen = Color(0xFF6BCF7F);

  @override
  void initState() {
    super.initState();

    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('Page started loading: $url');
          },
          onPageFinished: (String url) {
            print('Page finished loading: $url');
          },
        ),
      )
      ..addJavaScriptChannel(
        'gameEvents',
        onMessageReceived: (JavaScriptMessage message) {
          _handleGameEvent(message.message);
        },
      );

    _loadGame();
  }

  void _loadGame() {
    // Load game HTML content directly
    final gameHtml = _getGameHtml();
    controller.loadHtmlString(gameHtml);
  }

  String _getGameHtml() {
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking Rush</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .game-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .car-demo {
            font-size: 48px;
            margin: 20px 0;
            animation: carMove 2s ease-in-out infinite;
        }
        @keyframes carMove {
            0%, 100% { transform: translateX(-20px); }
            50% { transform: translateX(20px); }
        }
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        button {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        .win-btn {
            background: #4CAF50;
            color: white;
        }
        .lose-btn {
            background: #ff6b6b;
            color: white;
        }
        .play-btn {
            background: #2196F3;
            color: white;
        }
        button:hover {
            transform: scale(1.05);
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
        }
        .game-description {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🚗 Parking Rush</h1>
        <div class="car-demo">🚗💨</div>

        <div class="game-description">
            <h3>🎮 How to Play:</h3>
            <p>• Draw paths from colored cars to matching parking spots</p>
            <p>• Avoid car collisions while crossing paths</p>
            <p>• Complete all levels to earn maximum rewards</p>
            <p>• Use strategy to solve parking puzzles</p>
        </div>

        <div class="buttons">
            <button class="play-btn" onclick="startGame()">
                🎮 Start Game
            </button>
            <button class="win-btn" onclick="sendWin()">
                🎉 Win Demo
            </button>
            <button class="lose-btn" onclick="sendLose()">
                💥 Lose Demo
            </button>
        </div>

        <div class="info">
            <p><strong>Parking Rush</strong> - Paint the way for cars!</p>
            <p>Draw safe paths to parking spots without accidents.</p>
            <p>40 exciting levels with increasing difficulty!</p>
        </div>
    </div>

    <script>
        function startGame() {
            alert('🎮 Game Starting! Draw paths carefully to avoid crashes!');
            // Simulate game play
            setTimeout(() => {
                if (Math.random() > 0.5) {
                    sendWin();
                } else {
                    sendLose();
                }
            }, 3000);
        }

        function sendWin() {
            console.log('Sending win event');
            if (window.gameEvents && window.gameEvents.postMessage) {
                window.gameEvents.postMessage('win');
            }
            alert('🎉 Level Complete! You earned ₹15!');
        }

        function sendLose() {
            console.log('Sending lose event');
            if (window.gameEvents && window.gameEvents.postMessage) {
                window.gameEvents.postMessage('lose');
            }
            alert('💥 Cars crashed! Try again!');
        }

        // Auto-load message
        setTimeout(() => {
            console.log('Parking Rush game loaded successfully!');
        }, 1000);
    </script>
</body>
</html>
    ''';
  }

  void _handleGameEvent(String event) {
    switch (event) {
      case 'win':
        _showInterstitialAndReward(true);
        break;
      case 'lose':
        _showInterstitialAndReward(false);
        break;
    }
  }

  Future<void> _showInterstitialAndReward(bool isWin) async {
    // Show interstitial ad after game completion
    try {
      final adShown = await _appodealService.showInterstitial();
      if (adShown) {
        // Wait a bit for ad to complete, then show reward dialog
        await Future.delayed(const Duration(seconds: 1));
      }
    } catch (e) {
      print('Failed to show interstitial ad: $e');
    }

    // Show reward dialog regardless of ad success
    _showRewardDialog(isWin);
  }

  void _showRewardDialog(bool isWin) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          isWin ? '🎉 You Won!' : '💔 Game Over',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: isWin ? mintGreen : coral,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              isWin
                ? 'Congratulations! You completed the level.'
                : 'Better luck next time! Try again.',
              style: const TextStyle(fontSize: 16),
            ),
            if (isWin) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [mintGreen, Color(0xFF4CAF50)]),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  '₹15 added to Partial Wallet',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('Back to Games'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadGame(); // Restart game
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isWin ? mintGreen : coral,
              foregroundColor: Colors.white,
            ),
            child: Text(isWin ? 'Play Again' : 'Try Again'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.gameTitle),
        backgroundColor: Colors.white,
        foregroundColor: navyBlue,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _loadGame(),
            icon: const Icon(Icons.refresh_rounded),
          ),
        ],
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}
