import 'package:flutter/material.dart';
import '../services/pocketbase_service.dart';

class GamesScreen extends StatefulWidget {
  const GamesScreen({super.key});

  @override
  State<GamesScreen> createState() => _GamesScreenState();
}

class _GamesScreenState extends State<GamesScreen> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotateController;
  double _balance = 0.0;
  bool _isLoading = true;

  final List<GameItem> _games = [
    GameItem(title: 'Coin Collector', reward: 5.0, difficulty: 'Easy', category: 'Arcade'),
    GameItem(title: 'Crypto Runner', reward: 7.5, difficulty: 'Medium', category: 'Adventure'),
    GameItem(title: 'Money Puzzle', reward: 3.0, difficulty: 'Easy', category: 'Puzzle'),
    GameItem(title: 'Trading Simulator', reward: 10.0, difficulty: 'Hard', category: 'Strategy'),
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _rotateController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();
    
    _loadBalance();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  Future<void> _loadBalance() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final balance = await pocketBaseService.getUserBalance();
      setState(() {
        _balance = balance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0F0F23),
              const Color(0xFF1A1A2E),
              const Color(0xFF533483),
              const Color(0xFF16213E),
            ],
          ),
        ),
        child: Stack(
          children: [
            _buildAnimatedBackground(),
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildBalanceCard(),
                  Expanded(child: _buildGamesGrid()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: _rotateController,
          builder: (context, child) {
            return Positioned(
              top: 80,
              right: 20,
              child: Transform.rotate(
                angle: _rotateController.value * 2 * 3.14159,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.purple.withOpacity(0.3), width: 2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            );
          },
        ),
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Positioned(
              bottom: 100,
              left: 30,
              child: Container(
                width: 30 + (_pulseController.value * 15),
                height: 30 + (_pulseController.value * 15),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [Colors.purple.withOpacity(0.4), Colors.transparent],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Colors.purple.withOpacity(0.3), Colors.pink.withOpacity(0.3)],
              ),
              border: Border.all(color: Colors.purple.withOpacity(0.5), width: 2),
            ),
            child: const Icon(Icons.games_rounded, color: Colors.purple, size: 24),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Game Zone',
                  style: TextStyle(
                    fontFamily: 'Orbitron',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [Shadow(color: Colors.purple.withOpacity(0.5), blurRadius: 10)],
                  ),
                ),
                Text(
                  'Play games and earn rewards',
                  style: TextStyle(fontFamily: 'Exo2', fontSize: 14, color: Colors.white70),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple.withOpacity(0.3),
            Colors.indigo.withOpacity(0.3),
            Colors.pink.withOpacity(0.2),
          ],
        ),
        border: Border.all(color: Colors.purple.withOpacity(0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.2),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your Balance',
                style: TextStyle(fontFamily: 'Exo2', fontSize: 14, color: Colors.white70),
              ),
              const SizedBox(height: 5),
              _isLoading
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
                    )
                  : Text(
                      '\$${_balance.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontFamily: 'Orbitron',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                        shadows: [Shadow(color: Colors.purple.withOpacity(0.5), blurRadius: 10)],
                      ),
                    ),
            ],
          ),
          ElevatedButton(
            onPressed: _handleWithdraw,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple.withOpacity(0.2),
              foregroundColor: Colors.purple,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: Text(
              'Withdraw',
              style: TextStyle(fontFamily: 'Orbitron', fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGamesGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.9,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
        ),
        itemCount: _games.length,
        itemBuilder: (context, index) => _buildGameCard(_games[index]),
      ),
    );
  }

  Widget _buildGameCard(GameItem game) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white.withOpacity(0.1), Colors.white.withOpacity(0.05)],
        ),
        border: Border.all(color: _getDifficultyColor(game.difficulty).withOpacity(0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: _getDifficultyColor(game.difficulty).withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getDifficultyColor(game.difficulty).withOpacity(0.2),
                  ),
                  child: Icon(
                    Icons.games_rounded,
                    color: _getDifficultyColor(game.difficulty),
                    size: 20,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: _getDifficultyColor(game.difficulty).withOpacity(0.2),
                  ),
                  child: Text(
                    game.difficulty,
                    style: TextStyle(
                      fontFamily: 'Exo2',
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: _getDifficultyColor(game.difficulty),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Text(
              game.title,
              style: TextStyle(
                fontFamily: 'Orbitron',
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              game.category,
              style: TextStyle(fontFamily: 'Exo2', fontSize: 12, color: Colors.white60),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [Colors.green.withOpacity(0.3), Colors.cyan.withOpacity(0.3)],
                    ),
                  ),
                  child: Text(
                    '\$${game.reward.toStringAsFixed(1)}',
                    style: TextStyle(
                      fontFamily: 'Orbitron',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => _playGame(game),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.purple.withOpacity(0.2),
                      border: Border.all(color: Colors.purple.withOpacity(0.5), width: 1),
                    ),
                    child: const Icon(Icons.play_arrow_rounded, color: Colors.purple, size: 18),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy': return Colors.green;
      case 'medium': return Colors.orange;
      case 'hard': return Colors.red;
      default: return Colors.purple;
    }
  }

  void _playGame(GameItem game) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting game: ${game.title}'),
        backgroundColor: Colors.purple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  Future<void> _handleWithdraw() async {
    if (!pocketBaseService.isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login to withdraw'), backgroundColor: Colors.orange),
      );
      return;
    }

    if (_balance < 5.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Minimum withdrawal amount is \$5.00'), backgroundColor: Colors.orange),
      );
      return;
    }

    final result = await pocketBaseService.requestWithdrawal(_balance);
    
    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result['message']), backgroundColor: Colors.green),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${result['error']}'), backgroundColor: Colors.red),
      );
    }
  }
}

class GameItem {
  final String title;
  final double reward;
  final String difficulty;
  final String category;

  GameItem({required this.title, required this.reward, required this.difficulty, required this.category});
}
