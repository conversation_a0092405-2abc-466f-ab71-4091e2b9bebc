import 'package:flutter/material.dart';
import 'dart:math' as math;

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _floatController;
  late AnimationController _pulseController;
  late AnimationController _rotateController;

  // Summer Splash Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);

  @override
  void initState() {
    super.initState();
    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _floatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _rotateController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _floatController.dispose();
    _pulseController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              babyBlue,
              blueGreen,
              blueGrotto,
              navyBlue,
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Animated wave background
            _buildWaveBackground(),

            // Floating elements
            _buildFloatingElements(),

            SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // Header Section
                    _buildSummerHeader(),
                    const SizedBox(height: 30),

                    // Balance Card
                    _buildSummerBalanceCard(),
                    const SizedBox(height: 30),

                    // Quick Actions
                    _buildQuickActions(),
                    const SizedBox(height: 30),

                    // Feature Cards
                    _buildFeatureCards(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveBackground() {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        return CustomPaint(
          painter: WavePainter(_waveController.value),
          size: Size.infinite,
        );
      },
    );
  }

  Widget _buildFloatingElements() {
    return Stack(
      children: [
        // Floating bubbles
        ...List.generate(8, (index) {
          return AnimatedBuilder(
            animation: _floatController,
            builder: (context, child) {
              final offset = (index * 0.3) % 1.0;
              final x = (index * 50.0) % MediaQuery.of(context).size.width;
              final y = 100 + (index * 80.0) % 400;
              return Positioned(
                left: x,
                top: y + (_floatController.value * 30 * (index % 2 == 0 ? 1 : -1)),
                child: Transform.scale(
                  scale: 0.8 + (_pulseController.value * 0.4),
                  child: Container(
                    width: 20 + (index % 3) * 10,
                    height: 20 + (index % 3) * 10,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: [babyBlue, blueGreen, blueGrotto][index % 3].withOpacity(0.3),
                      boxShadow: [
                        BoxShadow(
                          color: [babyBlue, blueGreen, blueGrotto][index % 3].withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        }),

        // Floating sea elements
        AnimatedBuilder(
          animation: _rotateController,
          builder: (context, child) {
            return Positioned(
              top: 150,
              right: 30,
              child: Transform.rotate(
                angle: _rotateController.value * 2 * math.pi,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: blueGreen.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Center(
                    child: Text('🌊', style: TextStyle(fontSize: 20)),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSummerHeader() {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            babyBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: blueGrotto, width: 3),
        boxShadow: [
          BoxShadow(
            color: blueGrotto.withOpacity(0.3),
            blurRadius: 25,
            offset: const Offset(0, 15),
            spreadRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          // Animated summer avatar
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.1),
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [blueGrotto, navyBlue],
                    ),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 4),
                    boxShadow: [
                      BoxShadow(
                        color: blueGrotto.withOpacity(0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Center(
                    child: Text(
                      '🏄‍♂️',
                      style: TextStyle(fontSize: 30),
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Summer Vibes! 🌊',
                  style: TextStyle(
                    fontSize: 16,
                    color: navyBlue.withOpacity(0.8),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 6),
                const Text(
                  'Ready to Surf & Earn?',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w900,
                    color: navyBlue,
                  ),
                ),
              ],
            ),
          ),
          // Animated notification
          AnimatedBuilder(
            animation: _rotateController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotateController.value * 0.5,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [blueGreen, blueGrotto],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: blueGreen.withOpacity(0.4),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Text(
                    '🔔',
                    style: TextStyle(fontSize: 24),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSummerBalanceCard() {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                navyBlue,
                blueGrotto,
                blueGreen,
              ],
            ),
            borderRadius: BorderRadius.circular(35),
            border: Border.all(color: Colors.white, width: 4),
            boxShadow: [
              BoxShadow(
                color: navyBlue.withOpacity(0.4),
                blurRadius: 30,
                offset: const Offset(0, 20),
                spreadRadius: 8,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Wave overlay
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(31),
                  child: CustomPaint(
                    painter: BalanceWavePainter(_waveController.value),
                  ),
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(25),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Row(
                          children: [
                            Text(
                              '🏖️ ',
                              style: TextStyle(fontSize: 24),
                            ),
                            Text(
                              'Beach Wallet',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                        AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (_pulseController.value * 0.1),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.9),
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(color: blueGreen, width: 2),
                                ),
                                child: const Text(
                                  '🌊 WAVES',
                                  style: TextStyle(
                                    color: navyBlue,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w800,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    const Spacer(),
                    const Text(
                      '\$2,847.50',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 48,
                        fontWeight: FontWeight.w900,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 15,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        '🚀 +\$127.50 this week! Riding the wave!',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        // Title
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [blueGrotto, navyBlue],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                '⚡',
                style: TextStyle(fontSize: 24),
              ),
            ),
            const SizedBox(width: 15),
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.w900,
                color: navyBlue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 25),

        // Action buttons row
        Row(
          children: [
            Expanded(
              child: _buildQuickActionButton(
                'Tasks',
                '📋',
                blueGrotto,
                () {},
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildQuickActionButton(
                'Videos',
                '🎬',
                blueGreen,
                () {},
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildQuickActionButton(
                'Games',
                '🎮',
                navyBlue,
                () {},
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCards() {
    return Column(
      children: [
        // Title
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [blueGreen, blueGrotto],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                '🌊',
                style: TextStyle(fontSize: 24),
              ),
            ),
            const SizedBox(width: 15),
            const Text(
              'Surf & Earn',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.w900,
                color: navyBlue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 25),

        // Feature cards
        Row(
          children: [
            Expanded(
              child: _buildFeatureCard(
                'Daily Bonus',
                'Catch the wave!',
                '🏄‍♂️',
                navyBlue,
                () {},
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildFeatureCard(
                'Referrals',
                'Invite friends!',
                '🤝',
                blueGrotto,
                () {},
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),

        // Profile card
        _buildProfileCard(),
      ],
    );
  }

  Widget _buildQuickActionButton(String title, String emoji, Color color, VoidCallback onTap) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return GestureDetector(
          onTap: onTap,
          child: Transform.scale(
            scale: 1.0 + (_pulseController.value * 0.05),
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white,
                    color.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: color, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, color.withOpacity(0.8)],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        emoji,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: TextStyle(
                      color: color,
                      fontSize: 16,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureCard(String title, String subtitle, String emoji, Color color, VoidCallback onTap) {
    return AnimatedBuilder(
      animation: _floatController,
      builder: (context, child) {
        return GestureDetector(
          onTap: onTap,
          child: Transform.translate(
            offset: Offset(0, _floatController.value * 8),
            child: Container(
              height: 140,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color,
                    color.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 12),
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 45,
                    height: 45,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(22),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        emoji,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileCard() {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(25),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                babyBlue,
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: blueGreen, width: 3),
            boxShadow: [
              BoxShadow(
                color: blueGreen.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 3,
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [blueGrotto, navyBlue],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(color: Colors.white, width: 3),
                ),
                child: const Center(
                  child: Text(
                    '👤',
                    style: TextStyle(fontSize: 24),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Profile',
                      style: TextStyle(
                        color: navyBlue,
                        fontSize: 20,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Manage account & settings',
                      style: TextStyle(
                        color: navyBlue,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [blueGreen, blueGrotto],
                  ),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Custom painters for wave effects
class WavePainter extends CustomPainter {
  final double animationValue;

  WavePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF75E6DA).withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 30.0;
    final waveLength = size.width / 2;

    path.moveTo(0, size.height * 0.8);

    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height * 0.8 +
          waveHeight * math.sin((x / waveLength * 2 * math.pi) + (animationValue * 2 * math.pi));
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class BalanceWavePainter extends CustomPainter {
  final double animationValue;

  BalanceWavePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF75E6DA).withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 20.0;
    final waveLength = size.width / 3;

    path.moveTo(0, size.height * 0.7);

    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height * 0.7 +
          waveHeight * math.sin((x / waveLength * 2 * math.pi) + (animationValue * 4 * math.pi));
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
