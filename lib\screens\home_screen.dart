import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../services/pocketbase_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  double _balance = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _loadBalance();
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _loadBalance() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final balance = await pocketBaseService.getUserBalance();
      setState(() {
        _balance = balance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF2D1B69),  // Deep purple
              const Color(0xFF11052C),  // Very dark purple
            ],
          ),
        ),
        child: Stack(
          children: [
            // Animated background stars and sparkles
            _buildAnimatedBackground(),

            // Main content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with welcome message
                    _buildAnimeHeader(),
                    const SizedBox(height: 20),

                    // Wallet cards section
                    _buildWalletSection(),
                    const SizedBox(height: 30),

                    // Action buttons grid
                    _buildActionGrid(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Stack(
      children: [
        // Animated stars
        ...List.generate(20, (index) {
          return AnimatedBuilder(
            animation: _floatingController,
            builder: (context, child) {
              final offset = (index * 0.1) % 1.0;
              final x = (index * 37) % MediaQuery.of(context).size.width;
              final y = 50 + (index * 43) % 400;
              return Positioned(
                left: x,
                top: y + (_floatingController.value * 20 * (index % 2 == 0 ? 1 : -1)),
                child: Container(
                  width: 4 + (index % 3),
                  height: 4 + (index % 3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.6 + (_pulseController.value * 0.4)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.3),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }),

        // Sparkle effects
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Positioned(
              top: 80 + (_pulseController.value * 10),
              right: 30,
              child: Transform.rotate(
                angle: _pulseController.value * 6.28,
                child: Icon(
                  Icons.auto_awesome,
                  color: Colors.yellow.withOpacity(0.7),
                  size: 20,
                ),
              ),
            );
          },
        ),

        AnimatedBuilder(
          animation: _floatingController,
          builder: (context, child) {
            return Positioned(
              bottom: 150 + (_floatingController.value * 30),
              left: 40,
              child: Transform.rotate(
                angle: _floatingController.value * 3.14,
                child: Icon(
                  Icons.auto_awesome,
                  color: Colors.cyan.withOpacity(0.6),
                  size: 16,
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAnimeHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back,',
                  style: TextStyle(
                    fontFamily: 'Exo2',
                    fontSize: 18,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Alex!',
                  style: TextStyle(
                    fontFamily: 'Orbitron',
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.yellow.withOpacity(0.5),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Animated coin with sparkles
            AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseController.value * 0.1),
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.yellow,
                          Colors.orange,
                          Colors.amber.shade700,
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.yellow.withOpacity(0.4),
                          blurRadius: 15,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.monetization_on,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWalletSection() {
    return Column(
      children: [
        // Partial Wallet Card
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF6A4C93).withOpacity(0.8),
                const Color(0xFF9B59B6).withOpacity(0.6),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.purple.withOpacity(0.3),
                blurRadius: 15,
                spreadRadius: 2,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/icons/wallet.svg',
                width: 40,
                height: 40,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Partial Wallet',
                      style: TextStyle(
                        fontFamily: 'Exo2',
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      '₹ 53',
                      style: TextStyle(
                        fontFamily: 'Orbitron',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.yellow.withOpacity(0.5),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),

        // Real Wallet Card
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF8E44AD).withOpacity(0.8),
                const Color(0xFF3498DB).withOpacity(0.6),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                blurRadius: 15,
                spreadRadius: 2,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/icons/golden_coin.svg',
                width: 40,
                height: 40,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Real Wallet',
                      style: TextStyle(
                        fontFamily: 'Exo2',
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      '₹ 230',
                      style: TextStyle(
                        fontFamily: 'Orbitron',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.cyan.withOpacity(0.5),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 15,
      mainAxisSpacing: 15,
      childAspectRatio: 1.2,
      children: [
        _buildActionCard(
          'Tasks',
          'assets/icons/tasks.svg',
          const Color(0xFFE74C3C),
          const Color(0xFFFF6B6B),
        ),
        _buildActionCard(
          'Watch Ads',
          'assets/icons/video.svg',
          const Color(0xFF3498DB),
          const Color(0xFF74B9FF),
        ),
        _buildActionCard(
          'Play Games',
          'assets/icons/games.svg',
          const Color(0xFF9B59B6),
          const Color(0xFFA29BFE),
        ),
        _buildActionCard(
          'Withdraw',
          'assets/icons/withdraw.svg',
          const Color(0xFFF39C12),
          const Color(0xFFFFD93D),
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, String iconPath, Color startColor, Color endColor) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            startColor.withOpacity(0.8),
            endColor.withOpacity(0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: startColor.withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // Handle navigation based on title
            switch (title) {
              case 'Tasks':
                // Navigate to tasks
                break;
              case 'Watch Ads':
                // Navigate to videos
                break;
              case 'Play Games':
                // Navigate to games
                break;
              case 'Withdraw':
                // Navigate to withdraw
                break;
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  iconPath,
                  width: 50,
                  height: 50,
                ),
                const SizedBox(height: 15),
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Orbitron',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}
