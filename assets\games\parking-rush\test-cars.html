<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car SVG Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c2c2c;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .car-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .car-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #555;
        }
        .car-item h3 {
            margin-bottom: 15px;
            color: #FFD700;
        }
        .car-svg {
            width: 80px;
            height: 80px;
            margin: 10px;
            border: 2px solid #666;
            background: white;
            border-radius: 5px;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            border-radius: 5px;
        }
        .loaded {
            background: #4CAF50;
            color: white;
        }
        .failed {
            background: #f44336;
            color: white;
        }
        .loading {
            background: #ff9800;
            color: white;
        }
        .game-link {
            display: inline-block;
            margin: 20px 10px;
            padding: 15px 30px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .game-link:hover {
            background: #45a049;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚗 Car SVG Loading Test</h1>
        <p>Testing if car SVG files can be loaded properly for the Parking Rush game.</p>
        
        <div class="car-grid">
            <div class="car-item">
                <h3>Red Car</h3>
                <img id="redCar" class="car-svg" src="../../red car.svg" alt="Red Car">
                <div id="redStatus" class="status loading">Loading...</div>
            </div>
            
            <div class="car-item">
                <h3>Blue Car</h3>
                <img id="blueCar" class="car-svg" src="../../blue car.svg" alt="Blue Car">
                <div id="blueStatus" class="status loading">Loading...</div>
            </div>
            
            <div class="car-item">
                <h3>Green Car</h3>
                <img id="greenCar" class="car-svg" src="../../green car.svg" alt="Green Car">
                <div id="greenStatus" class="status loading">Loading...</div>
            </div>
            
            <div class="car-item">
                <h3>Yellow Car</h3>
                <img id="yellowCar" class="car-svg" src="../../yellow car.svg" alt="Yellow Car">
                <div id="yellowStatus" class="status loading">Loading...</div>
            </div>
        </div>
        
        <div style="margin-top: 40px;">
            <h2>🎮 Game Links</h2>
            <a href="standalone.html" class="game-link">Play Full Game</a>
            <a href="launcher.html" class="game-link">Game Launcher</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
            <h3>📋 Test Results</h3>
            <p id="testResults">Testing car SVG loading...</p>
        </div>
    </div>

    <script>
        const cars = ['red', 'blue', 'green', 'yellow'];
        let loadedCount = 0;
        let failedCount = 0;
        
        cars.forEach(color => {
            const img = document.getElementById(`${color}Car`);
            const status = document.getElementById(`${color}Status`);
            
            img.onload = function() {
                status.textContent = 'Loaded Successfully!';
                status.className = 'status loaded';
                loadedCount++;
                updateResults();
            };
            
            img.onerror = function() {
                status.textContent = 'Failed to Load';
                status.className = 'status failed';
                failedCount++;
                updateResults();
            };
        });
        
        function updateResults() {
            const total = cars.length;
            const tested = loadedCount + failedCount;
            
            if (tested === total) {
                const results = document.getElementById('testResults');
                if (loadedCount === total) {
                    results.innerHTML = `✅ All ${total} car SVGs loaded successfully! The game should work perfectly.`;
                    results.style.color = '#4CAF50';
                } else if (loadedCount > 0) {
                    results.innerHTML = `⚠️ ${loadedCount}/${total} car SVGs loaded. ${failedCount} failed. Game will use fallback graphics for failed cars.`;
                    results.style.color = '#ff9800';
                } else {
                    results.innerHTML = `❌ No car SVGs loaded. Game will use fallback rectangular cars.`;
                    results.style.color = '#f44336';
                }
            }
        }
        
        // Timeout check
        setTimeout(() => {
            if (loadedCount + failedCount < cars.length) {
                const results = document.getElementById('testResults');
                results.innerHTML = `⏱️ Some car SVGs are taking too long to load. Check file paths.`;
                results.style.color = '#ff9800';
            }
        }, 5000);
    </script>
</body>
</html>
