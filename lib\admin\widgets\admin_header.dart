import 'package:flutter/material.dart';
import '../../services/pocketbase_service.dart';

class AdminHeader extends StatefulWidget {
  final String title;
  final VoidCallback onMenuPressed;

  const AdminHeader({
    super.key,
    required this.title,
    required this.onMenuPressed,
  });

  @override
  State<AdminHeader> createState() => _AdminHeaderState();
}

class _AdminHeaderState extends State<AdminHeader> {
  int _onlineUsers = 0;
  int _totalUsers = 0;
  double _todayRevenue = 0.0;

  // Admin Color Scheme
  static const Color primaryBlue = Color(0xFF1565C0);
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color successGreen = Color(0xFF4CAF50);

  @override
  void initState() {
    super.initState();
    _loadHeaderStats();
  }

  Future<void> _loadHeaderStats() async {
    try {
      // Load real-time stats from PocketBase
      // This would be implemented with actual API calls
      setState(() {
        _onlineUsers = 127; // Mock data
        _totalUsers = 2543; // Mock data
        _todayRevenue = 1247.50; // Mock data
      });
    } catch (e) {
      print('Error loading header stats: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Row(
          children: [
            // Menu Button (for mobile)
            if (MediaQuery.of(context).size.width < 1200)
              IconButton(
                onPressed: widget.onMenuPressed,
                icon: const Icon(Icons.menu),
                color: primaryBlue,
              ),
            
            // Page Title
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1565C0),
                    ),
                  ),
                  Text(
                    'Easy Money Admin Panel',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            
            // Quick Stats
            Row(
              children: [
                _buildQuickStat(
                  icon: Icons.people,
                  label: 'Online',
                  value: _onlineUsers.toString(),
                  color: successGreen,
                ),
                const SizedBox(width: 24),
                _buildQuickStat(
                  icon: Icons.group,
                  label: 'Total Users',
                  value: _totalUsers.toString(),
                  color: primaryBlue,
                ),
                const SizedBox(width: 24),
                _buildQuickStat(
                  icon: Icons.monetization_on,
                  label: 'Today Revenue',
                  value: '\$${_todayRevenue.toStringAsFixed(2)}',
                  color: accentOrange,
                ),
              ],
            ),
            
            const SizedBox(width: 24),
            
            // Notifications & Profile
            Row(
              children: [
                // Notifications
                Stack(
                  children: [
                    IconButton(
                      onPressed: () {
                        // Show notifications
                      },
                      icon: const Icon(Icons.notifications_outlined),
                      color: primaryBlue,
                    ),
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // Admin Profile
                PopupMenuButton<String>(
                  offset: const Offset(0, 50),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 18,
                        backgroundColor: primaryBlue,
                        child: const Icon(
                          Icons.admin_panel_settings,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pocketBaseService.currentUser?['name'] ?? 'Admin',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1565C0),
                            ),
                          ),
                          Text(
                            'Administrator',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'profile',
                      child: Row(
                        children: [
                          Icon(Icons.person_outline),
                          SizedBox(width: 12),
                          Text('Profile Settings'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          Icon(Icons.settings_outlined),
                          SizedBox(width: 12),
                          Text('System Settings'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.logout, color: Colors.red),
                          SizedBox(width: 12),
                          Text('Logout', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) async {
                    switch (value) {
                      case 'logout':
                        await pocketBaseService.logout();
                        if (context.mounted) {
                          Navigator.of(context).pushReplacementNamed('/admin-login');
                        }
                        break;
                      case 'profile':
                        // Show profile settings
                        break;
                      case 'settings':
                        // Show system settings
                        break;
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: color.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
