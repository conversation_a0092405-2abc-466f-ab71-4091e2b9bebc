<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Parking Rush - Standalone Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            touch-action: none;
            user-select: none;
            height: 100vh;
        }

        #gameContainer {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Game Header */
        #gameHeader {
            background: rgba(255, 255, 255, 0.95);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }

        #levelInfo {
            display: flex;
            gap: 20px;
            font-weight: bold;
            color: #333;
        }

        #restartBtn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #restartBtn:hover {
            background: #ff5252;
            transform: scale(1.05);
        }

        /* Game Canvas */
        #gameCanvas {
            flex: 1;
            background: #f0f8ff;
            cursor: crosshair;
            display: block;
        }

        /* Game Controls */
        #gameControls {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        #startBtn, #clearBtn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        #startBtn {
            background: #4CAF50;
            color: white;
        }

        #startBtn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        #startBtn:not(:disabled):hover {
            background: #45a049;
            transform: scale(1.05);
        }

        #clearBtn {
            background: #ff9800;
            color: white;
        }

        #clearBtn:hover {
            background: #f57c00;
            transform: scale(1.05);
        }

        /* Game Status */
        #gameStatus {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 5;
        }

        #instructions {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            text-align: center;
            animation: fadeInOut 4s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            25%, 75% { opacity: 1; }
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.3s ease;
        }

        .modal.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            max-width: 90%;
            width: 300px;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.8) translateY(-50px);
                opacity: 0;
            }
            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        #modalIcon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .modal-content h2 {
            color: #333;
            margin-bottom: 10px;
        }

        .modal-content p {
            color: #666;
            margin-bottom: 20px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal-buttons button {
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #nextLevelBtn {
            background: #4CAF50;
            color: white;
        }

        #retryBtn {
            background: #ff6b6b;
            color: white;
        }

        .modal-buttons button:hover {
            transform: scale(1.05);
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            #gameHeader {
                padding: 8px 15px;
                font-size: 14px;
            }
            
            #gameControls {
                padding: 10px 15px;
                gap: 15px;
            }
            
            #startBtn, #clearBtn {
                padding: 10px 20px;
                font-size: 14px;
                min-width: 100px;
            }
            
            .modal-content {
                padding: 20px;
                width: 280px;
            }
        }

        /* Game-specific styles */
        .car {
            border-radius: 50%;
            border: 3px solid #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .parking-spot {
            border: 3px dashed #333;
            border-radius: 10px;
            opacity: 0.7;
        }

        .path-line {
            stroke-width: 4;
            stroke-linecap: round;
            stroke-linejoin: round;
            fill: none;
            opacity: 0.8;
        }

        /* Welcome Screen */
        #welcomeScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            transition: opacity 0.5s ease;
        }

        .welcome-content {
            text-align: center;
            color: white;
            max-width: 400px;
            padding: 20px;
        }

        .welcome-content h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .welcome-content p {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.5;
        }

        #startGameBtn {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #startGameBtn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- Welcome Screen -->
    <div id="welcomeScreen">
        <div class="welcome-content">
            <h1>🚗 Parking Rush</h1>
            <p>Draw paths for cars to reach their parking spots safely. Avoid collisions and complete all levels!</p>
            <button id="startGameBtn">Start Playing</button>
        </div>
    </div>

    <div id="gameContainer">
        <!-- Game Header -->
        <div id="gameHeader">
            <div id="levelInfo">
                <span id="levelText">Level 1</span>
                <span id="scoreText">Score: 0</span>
            </div>
            <button id="restartBtn">🔄 Restart</button>
        </div>

        <!-- Game Canvas -->
        <canvas id="gameCanvas"></canvas>

        <!-- Game Controls -->
        <div id="gameControls">
            <button id="startBtn" disabled>🚗 Start Cars</button>
            <button id="clearBtn">🗑️ Clear Paths</button>
        </div>

        <!-- Game Status -->
        <div id="gameStatus">
            <div id="instructions">
                Draw paths from cars to matching colored parking spots!
            </div>
        </div>

        <!-- Win/Lose Modal -->
        <div id="gameModal" class="modal hidden">
            <div class="modal-content">
                <div id="modalIcon">🎉</div>
                <h2 id="modalTitle">Level Complete!</h2>
                <p id="modalMessage">Great job! All cars parked safely.</p>
                <div class="modal-buttons">
                    <button id="nextLevelBtn">Next Level</button>
                    <button id="retryBtn">Try Again</button>
                </div>
            </div>
        </div>
    </div>

    <script src="parking-rush-game.js"></script>
</body>
</html>
