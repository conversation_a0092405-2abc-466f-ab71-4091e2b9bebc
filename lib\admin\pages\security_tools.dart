import 'package:flutter/material.dart';

class SecurityTools extends StatefulWidget {
  const SecurityTools({super.key});

  @override
  State<SecurityTools> createState() => _SecurityToolsState();
}

class _SecurityToolsState extends State<SecurityTools> {
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Security & Fraud Detection\n\n'
        '• Device fingerprinting and tracking\n'
        '• Task completion speed monitoring\n'
        '• CAPTCHA validation logs\n'
        '• Suspicious activity detection\n'
        '• IP address monitoring\n'
        '• Automated fraud prevention',
        style: TextStyle(fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }
}
