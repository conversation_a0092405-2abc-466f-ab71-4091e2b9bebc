# 🔧 Network Connection Fix Instructions

## Problem
Your app is showing this error:
```
Network error: ClientException with SocketException: Connection refused (OS Error: Connection refused, errno = 111), address = 127.0.0.1, port = 41858
```

## Solution

### Step 1: Find Your Computer's IP Address

**On Windows:**
1. Press `Win + R`, type `cmd`, press Enter
2. Type: `ipconfig`
3. Look for "IPv4 Address" under your active network adapter
4. It will look like: `*************` or `*************` or similar

**On Mac/Linux:**
1. Open Terminal
2. Type: `ifconfig` or `ip addr show`
3. Look for your local IP address (usually starts with 192.168.x.x)

### Step 2: Update the App Configuration

1. Open `lib/services/pocketbase_service.dart`
2. Find line 9: `final String baseUrl = 'http://*************:8090';`
3. Replace `*************` with YOUR computer's IP address
4. Save the file

### Step 3: Make Sure PocketBase is Running

1. Open Command Prompt/Terminal
2. Navigate to your project folder: `cd g:\APPs\flr\easymoney`
3. Run: `setup_pocketbase.bat`
4. PocketBase should start on port 8090

### Step 4: Test the Connection

1. On your phone, open a web browser
2. Go to: `http://YOUR_IP_ADDRESS:8090/_/` (replace YOUR_IP_ADDRESS)
3. You should see the PocketBase admin interface
4. If this works, your app should connect successfully

### Step 5: Rebuild and Test

1. Stop your Flutter app
2. Run: `flutter clean && flutter pub get`
3. Rebuild and run the app on your device

## Example
If your computer's IP is `*************`, then:
- Update the baseUrl to: `http://*************:8090`
- Test in browser: `http://*************:8090/_/`

## Troubleshooting

**If still not working:**
1. Check Windows Firewall - allow port 8090
2. Make sure your phone and computer are on the same WiFi network
3. Try restarting PocketBase
4. Check if antivirus is blocking the connection

**Alternative for testing:**
- Use Android emulator instead of real device (works with 127.0.0.1)
- Or use `adb port forwarding`: `adb reverse tcp:8090 tcp:8090`
