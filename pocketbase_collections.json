{"collections": [{"name": "tasks", "type": "base", "schema": [{"name": "title", "type": "text", "required": true, "options": {"min": 3, "max": 100}}, {"name": "description", "type": "text", "required": false, "options": {"max": 500}}, {"name": "type", "type": "select", "required": true, "options": {"values": ["shortlink", "video", "game"]}}, {"name": "difficulty", "type": "select", "required": true, "options": {"values": ["easy", "medium", "hard"]}}, {"name": "reward_amount", "type": "number", "required": true, "options": {"min": 0.01, "max": 100}}, {"name": "original_reward", "type": "number", "required": true, "options": {"min": 0.01, "max": 100}}, {"name": "completion_goal", "type": "number", "required": true, "options": {"min": 1, "max": 10000}}, {"name": "current_completions", "type": "number", "required": false, "options": {"min": 0}}, {"name": "links", "type": "json", "required": false}, {"name": "active", "type": "bool", "required": false}, {"name": "expires_at", "type": "date", "required": false}], "listRule": "active = true || @request.auth.role = 'admin'", "viewRule": "active = true || @request.auth.role = 'admin'", "createRule": "@request.auth.role = 'admin'", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}, {"name": "completions", "type": "base", "schema": [{"name": "user", "type": "relation", "required": true, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": true}}, {"name": "task", "type": "relation", "required": true, "options": {"collectionId": "tasks", "cascadeDelete": true}}, {"name": "completed_at", "type": "date", "required": true}, {"name": "device_id", "type": "text", "required": true}, {"name": "ip_address", "type": "text", "required": true}, {"name": "completion_time", "type": "number", "required": false, "options": {"min": 0}}, {"name": "captcha_attempts", "type": "number", "required": false, "options": {"min": 1, "max": 10}}], "listRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "viewRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "createRule": "@request.auth.id = user.id", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}, {"name": "rewards", "type": "base", "schema": [{"name": "user", "type": "relation", "required": true, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": true}}, {"name": "task", "type": "relation", "required": false, "options": {"collectionId": "tasks", "cascadeDelete": false}}, {"name": "amount", "type": "number", "required": true, "options": {"min": 0.01}}, {"name": "source", "type": "select", "required": true, "options": {"values": ["task_completion", "bonus", "referral"]}}, {"name": "status", "type": "select", "required": true, "options": {"values": ["pending", "approved", "paid"]}}], "listRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "viewRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "createRule": "@request.auth.role = 'admin'", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}, {"name": "withdrawals", "type": "base", "schema": [{"name": "user", "type": "relation", "required": true, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": true}}, {"name": "amount", "type": "number", "required": true, "options": {"min": 1}}, {"name": "payment_method", "type": "select", "required": true, "options": {"values": ["paypal", "bank", "upi"]}}, {"name": "payment_details", "type": "json", "required": true}, {"name": "status", "type": "select", "required": true, "options": {"values": ["pending", "processing", "completed", "rejected"]}}, {"name": "requested_at", "type": "date", "required": true}, {"name": "processed_at", "type": "date", "required": false}, {"name": "admin_notes", "type": "text", "required": false}], "listRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "viewRule": "@request.auth.id = user.id || @request.auth.role = 'admin'", "createRule": "@request.auth.id = user.id", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}, {"name": "analytics", "type": "base", "schema": [{"name": "date", "type": "date", "required": true}, {"name": "total_users", "type": "number", "required": false, "options": {"min": 0}}, {"name": "active_users", "type": "number", "required": false, "options": {"min": 0}}, {"name": "tasks_completed", "type": "number", "required": false, "options": {"min": 0}}, {"name": "ads_played", "type": "number", "required": false, "options": {"min": 0}}, {"name": "revenue_generated", "type": "number", "required": false, "options": {"min": 0}}, {"name": "withdrawals_processed", "type": "number", "required": false, "options": {"min": 0}}], "listRule": "@request.auth.role = 'admin'", "viewRule": "@request.auth.role = 'admin'", "createRule": "@request.auth.role = 'admin'", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}, {"name": "admin_messages", "type": "base", "schema": [{"name": "title", "type": "text", "required": true, "options": {"min": 3, "max": 100}}, {"name": "message", "type": "text", "required": true, "options": {"min": 10, "max": 1000}}, {"name": "type", "type": "select", "required": true, "options": {"values": ["info", "warning", "success", "error"]}}, {"name": "target_users", "type": "select", "required": true, "options": {"values": ["all", "specific", "banned"]}}, {"name": "active", "type": "bool", "required": false}, {"name": "created_by", "type": "text", "required": true}, {"name": "expires_at", "type": "date", "required": false}], "listRule": "active = true || @request.auth.role = 'admin'", "viewRule": "active = true || @request.auth.role = 'admin'", "createRule": "@request.auth.role = 'admin'", "updateRule": "@request.auth.role = 'admin'", "deleteRule": "@request.auth.role = 'admin'"}], "users_additional_fields": [{"name": "banned", "type": "bool", "required": false}, {"name": "device_id", "type": "text", "required": false}, {"name": "last_active", "type": "date", "required": false}, {"name": "total_earnings", "type": "number", "required": false, "options": {"min": 0}}, {"name": "wallet_balance", "type": "number", "required": false, "options": {"min": 0}}, {"name": "role", "type": "select", "required": false, "options": {"values": ["user", "admin"]}}]}