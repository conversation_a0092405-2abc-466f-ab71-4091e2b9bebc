import 'package:flutter/material.dart';

class DashboardOverview extends StatefulWidget {
  const DashboardOverview({super.key});

  @override
  State<DashboardOverview> createState() => _DashboardOverviewState();
}

class _DashboardOverviewState extends State<DashboardOverview> {
  // Admin Color Scheme
  static const Color primaryBlue = Color(0xFF1565C0);
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFF44336);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Stats Cards - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth > 1200) {
                // Desktop: 4 cards in a row
                return Row(
                  children: [
                    Expanded(child: _buildStatCard(title: 'Total Users', value: '2,543', change: '+12.5%', icon: Icons.people, color: primaryBlue, isPositive: true)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard(title: 'Active Tasks', value: '47', change: '+3', icon: Icons.assignment, color: successGreen, isPositive: true)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard(title: 'Today Revenue', value: '\$1,247.50', change: '****%', icon: Icons.monetization_on, color: accentOrange, isPositive: true)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard(title: 'Pending Withdrawals', value: '23', change: '-2', icon: Icons.account_balance_wallet, color: warningAmber, isPositive: false)),
                  ],
                );
              } else if (constraints.maxWidth > 800) {
                // Tablet: 2 cards per row
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(child: _buildStatCard(title: 'Total Users', value: '2,543', change: '+12.5%', icon: Icons.people, color: primaryBlue, isPositive: true)),
                        const SizedBox(width: 16),
                        Expanded(child: _buildStatCard(title: 'Active Tasks', value: '47', change: '+3', icon: Icons.assignment, color: successGreen, isPositive: true)),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(child: _buildStatCard(title: 'Today Revenue', value: '\$1,247.50', change: '****%', icon: Icons.monetization_on, color: accentOrange, isPositive: true)),
                        const SizedBox(width: 16),
                        Expanded(child: _buildStatCard(title: 'Pending Withdrawals', value: '23', change: '-2', icon: Icons.account_balance_wallet, color: warningAmber, isPositive: false)),
                      ],
                    ),
                  ],
                );
              } else {
                // Mobile: 1 card per row
                return Column(
                  children: [
                    _buildStatCard(title: 'Total Users', value: '2,543', change: '+12.5%', icon: Icons.people, color: primaryBlue, isPositive: true),
                    const SizedBox(height: 16),
                    _buildStatCard(title: 'Active Tasks', value: '47', change: '+3', icon: Icons.assignment, color: successGreen, isPositive: true),
                    const SizedBox(height: 16),
                    _buildStatCard(title: 'Today Revenue', value: '\$1,247.50', change: '****%', icon: Icons.monetization_on, color: accentOrange, isPositive: true),
                    const SizedBox(height: 16),
                    _buildStatCard(title: 'Pending Withdrawals', value: '23', change: '-2', icon: Icons.account_balance_wallet, color: warningAmber, isPositive: false),
                  ],
                );
              }
            },
          ),
          
          const SizedBox(height: 32),
          
          // Charts and Analytics - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth > 800) {
                // Desktop/Tablet: Side by side
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Revenue Chart
                    Expanded(
                      flex: 2,
                      child: _buildChartCard(
                        title: 'Revenue Analytics',
                        subtitle: 'Last 30 days',
                        child: Container(
                          height: 300,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text(
                              'Revenue Chart\n(Chart.js integration)',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Recent Activity
                    Expanded(
                      child: _buildActivityCard(),
                    ),
                  ],
                );
              } else {
                // Mobile: Stacked
                return Column(
                  children: [
                    _buildChartCard(
                      title: 'Revenue Analytics',
                      subtitle: 'Last 30 days',
                      child: Container(
                        height: 250, // Reduced height for mobile
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Text(
                            'Revenue Chart\n(Chart.js integration)',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildActivityCard(),
                  ],
                );
              }
            },
          ),
          
          const SizedBox(height: 32),
          
          // Recent Users and Tasks - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth > 800) {
                // Desktop/Tablet: Side by side
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Recent Users
                    Expanded(
                      child: _buildRecentUsersCard(),
                    ),

                    const SizedBox(width: 16),

                    // Top Tasks
                    Expanded(
                      child: _buildTopTasksCard(),
                    ),
                  ],
                );
              } else {
                // Mobile: Stacked
                return Column(
                  children: [
                    _buildRecentUsersCard(),
                    const SizedBox(height: 16),
                    _buildTopTasksCard(),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String change,
    required IconData icon,
    required Color color,
    required bool isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isPositive 
                      ? successGreen.withOpacity(0.1)
                      : errorRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  change,
                  style: TextStyle(
                    color: isPositive ? successGreen : errorRed,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.w800,
              color: Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartCard({
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1565C0),
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.more_vert),
                color: Colors.grey.shade600,
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildActivityCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Activity',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 16),
          ...List.generate(5, (index) {
            final activities = [
              {'action': 'New user registered', 'time': '2 min ago', 'icon': Icons.person_add, 'color': successGreen},
              {'action': 'Task completed', 'time': '5 min ago', 'icon': Icons.check_circle, 'color': primaryBlue},
              {'action': 'Withdrawal requested', 'time': '12 min ago', 'icon': Icons.account_balance_wallet, 'color': warningAmber},
              {'action': 'New task created', 'time': '1 hour ago', 'icon': Icons.add_task, 'color': accentOrange},
              {'action': 'User banned', 'time': '2 hours ago', 'icon': Icons.block, 'color': errorRed},
            ];
            
            final activity = activities[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (activity['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      activity['icon'] as IconData,
                      color: activity['color'] as Color,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          activity['action'] as String,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          activity['time'] as String,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRecentUsersCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Recent Users',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1565C0),
                ),
              ),
              TextButton(
                onPressed: () {},
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...List.generate(4, (index) {
            final users = [
              {'name': 'John Smith', 'email': '<EMAIL>', 'earnings': '\$45.20'},
              {'name': 'Sarah Johnson', 'email': '<EMAIL>', 'earnings': '\$32.10'},
              {'name': 'Mike Wilson', 'email': '<EMAIL>', 'earnings': '\$28.50'},
              {'name': 'Emma Davis', 'email': '<EMAIL>', 'earnings': '\$51.80'},
            ];
            
            final user = users[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: primaryBlue,
                    child: Text(
                      (user['name'] as String)[0],
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user['name'] as String,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          user['email'] as String,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    user['earnings'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: successGreen,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTopTasksCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Top Performing Tasks',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1565C0),
                ),
              ),
              TextButton(
                onPressed: () {},
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...List.generate(4, (index) {
            final tasks = [
              {'title': 'Complete Tech Offers', 'completions': '1,234', 'reward': '\$2.50'},
              {'title': 'Watch Video Ads', 'completions': '987', 'reward': '\$1.00'},
              {'title': 'Gaming Challenges', 'completions': '756', 'reward': '\$3.00'},
              {'title': 'Survey Tasks', 'completions': '543', 'reward': '\$1.50'},
            ];
            
            final task = tasks[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: accentOrange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.assignment,
                      color: accentOrange,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          task['title'] as String,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${task['completions']} completions',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    task['reward'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: successGreen,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
