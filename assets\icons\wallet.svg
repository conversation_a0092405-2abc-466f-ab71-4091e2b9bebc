<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="walletGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8A2BE2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#9932CC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4B0082;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Wallet body -->
  <rect x="15" y="25" width="70" height="50" rx="8" ry="8" fill="url(#walletGradient)" stroke="#4B0082" stroke-width="2"/>
  
  <!-- Wallet flap -->
  <rect x="15" y="20" width="70" height="15" rx="8" ry="8" fill="url(#walletGradient)" stroke="#4B0082" stroke-width="2"/>
  
  <!-- Card sticking out -->
  <rect x="25" y="35" width="40" height="25" rx="4" ry="4" fill="url(#cardGradient)" stroke="#B8860B" stroke-width="1"/>
  
  <!-- Card details -->
  <rect x="30" y="40" width="15" height="2" fill="#B8860B" opacity="0.7"/>
  <rect x="30" y="45" width="25" height="2" fill="#B8860B" opacity="0.7"/>
  <rect x="30" y="50" width="20" height="2" fill="#B8860B" opacity="0.7"/>
  
  <!-- Money bills -->
  <rect x="20" y="30" width="35" height="20" rx="2" ry="2" fill="#90EE90" opacity="0.8" stroke="#228B22" stroke-width="1"/>
  <rect x="22" y="32" width="35" height="20" rx="2" ry="2" fill="#98FB98" opacity="0.8" stroke="#228B22" stroke-width="1"/>
  
  <!-- Dollar signs on bills -->
  <text x="32" y="45" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#228B22">$</text>
  <text x="45" y="45" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#228B22">$</text>
  
  <!-- Sparkles -->
  <polygon points="70,15 71,18 74,18 72,20 73,23 70,21 67,23 68,20 66,18 69,18" fill="#FFD700" opacity="0.9"/>
  <polygon points="85,35 86,37 88,37 87,38 88,40 85,39 82,40 83,38 82,37 84,37" fill="#FFD700" opacity="0.9"/>
</svg>
