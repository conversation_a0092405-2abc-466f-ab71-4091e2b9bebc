import 'package:flutter/material.dart';
import 'leaderboard_screen.dart';
import 'task_detail_screen.dart';
import '../services/global_ad_manager.dart';
import '../widgets/global_banner_ad.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> with TickerProviderStateMixin, GlobalAdMixin {
  late AnimationController _slideController;

  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              babyBlue,
              Color(0xFFE8F4FD),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Modern Header with Leaderboard
              _buildModernHeader(),

              // Tasks List
              Expanded(
                child: _buildTasksList(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const GlobalBannerAd(
        adId: 'Tasks Banner',
        height: 60,
      ),
    );
  }

  Widget _buildModernHeader() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📋 Available Tasks',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w900,
                        color: navyBlue,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '8 tasks available • Complete & earn rewards',
                      style: TextStyle(
                        fontSize: 14,
                        color: navyBlue.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const LeaderboardScreen()),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [goldenYellow, sunsetOrange],
                    ),
                    borderRadius: BorderRadius.circular(18),
                    boxShadow: [
                      BoxShadow(
                        color: goldenYellow.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.leaderboard_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  Widget _buildTasksList() {
    return ListView(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 100), // Extra bottom padding for banner
      children: [
        _buildShortLinkTaskCard(
          'TSK001',
          'Complete Tech Offers Challenge',
          'Easy',
          569,
          1000,
          '₹5.00',
          '₹3.00',
          [coral, sunsetOrange],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK002',
          'Shopping Deals Explorer',
          'Medium',
          234,
          800,
          '₹8.00',
          '₹6.00',
          [blueGrotto, blueGreen],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK003',
          'Educational Content Discovery',
          'Easy',
          789,
          1000,
          '₹4.00',
          '₹2.50',
          [mintGreen, lavender],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK004',
          'Gaming & Entertainment Quest',
          'Hard',
          156,
          1200,
          '₹12.00',
          '₹8.00',
          [goldenYellow, sunsetOrange],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK005',
          'Health & Fitness Journey',
          'Medium',
          445,
          900,
          '₹7.00',
          '₹5.00',
          [lavender, roseGold],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK006',
          'Travel & Tourism Adventure',
          'Hard',
          67,
          1500,
          '₹15.00',
          '₹10.00',
          [coral, goldenYellow],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK007',
          'Finance & Investment Guide',
          'Easy',
          892,
          1000,
          '₹6.00',
          '₹4.00',
          [blueGrotto, mintGreen],
        ),
        const SizedBox(height: 20),
        _buildShortLinkTaskCard(
          'TSK008',
          'Food & Restaurant Explorer',
          'Medium',
          334,
          750,
          '₹9.00',
          '₹6.50',
          [sunsetOrange, roseGold],
        ),
      ],
    );
  }

  Widget _buildShortLinkTaskCard(
    String taskId,
    String title,
    String difficulty,
    int currentProgress,
    int goalProgress,
    String currentReward,
    String originalReward,
    List<Color> colors,
  ) {
    final progressPercentage = currentProgress / goalProgress;
    final isCompleted = currentProgress >= goalProgress;

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1, 0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          border: isCompleted
            ? Border.all(color: mintGreen, width: 2)
            : null,
          boxShadow: [
            BoxShadow(
              color: colors[0].withOpacity(0.15),
              blurRadius: 25,
              offset: const Offset(0, 10),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: colors),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.task_alt_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            taskId,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: colors[0],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getDifficultyColor(difficulty).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              difficulty.toUpperCase(),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w700,
                                color: _getDifficultyColor(difficulty),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: navyBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Progress Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colors[0].withOpacity(0.05),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Progress',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: navyBlue,
                        ),
                      ),
                      Text(
                        '$currentProgress / $goalProgress',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: colors[0],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: progressPercentage.clamp(0.0, 1.0),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: colors),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${(progressPercentage * 100).toInt()}% Complete',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: colors[0],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Reward Section
            Row(
              children: [
                const Icon(
                  Icons.monetization_on_rounded,
                  color: goldenYellow,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  originalReward,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [goldenYellow, sunsetOrange]),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    currentReward,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                const Spacer(),
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    decoration: BoxDecoration(
                      color: coral.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '5 Points',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: coral,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Action Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isCompleted ? null : () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TaskDetailScreen(
                        taskId: taskId,
                        title: title,
                        difficulty: difficulty,
                        reward: currentReward,
                        colors: colors,
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isCompleted ? Colors.grey : colors[0],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  isCompleted ? 'Goal Reached' : 'Start Task',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return mintGreen;
      case 'medium':
        return goldenYellow;
      case 'hard':
        return coral;
      default:
        return navyBlue;
    }
  }
}
