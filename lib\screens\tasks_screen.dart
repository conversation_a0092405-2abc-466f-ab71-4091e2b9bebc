import 'package:flutter/material.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _sparkleController;

  @override
  void initState() {
    super.initState();
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFFB6C1), // Light pink
              Color(0xFFFFE4E1), // Misty rose
              Color(0xFFE0FFFF), // Light cyan
              Color(0xFFB0E0E6), // Powder blue
            ],
          ),
        ),
        child: Stack(
          children: [
            // Floating elements
            _buildFloatingElements(),

            SafeArea(
              child: Column(
                children: [
                  // Header
                  _buildHeader(context),

                  // Tasks List
                  Expanded(
                    child: _buildTasksList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingElements() {
    return Stack(
      children: [
        // Floating task icons
        AnimatedBuilder(
          animation: _bounceController,
          builder: (context, child) {
            return Positioned(
              top: 120 + (_bounceController.value * 15),
              right: 30,
              child: const Text(
                '📋',
                style: TextStyle(fontSize: 20),
              ),
            );
          },
        ),
        AnimatedBuilder(
          animation: _sparkleController,
          builder: (context, child) {
            return Positioned(
              top: 250 + (_sparkleController.value * 10),
              left: 40,
              child: Transform.rotate(
                angle: _sparkleController.value * 6.28,
                child: const Text(
                  '⭐',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            );
          },
        ),
        AnimatedBuilder(
          animation: _bounceController,
          builder: (context, child) {
            return Positioned(
              bottom: 200 + (_bounceController.value * 20),
              right: 50,
              child: const Text(
                '🎯',
                style: TextStyle(fontSize: 18),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6B9D), // Bright pink
            Color(0xFFFF8E9B), // Coral pink
          ],
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(35),
          bottomRight: Radius.circular(35),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Text(
                    '⬅️',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Row(
                  children: [
                    Text(
                      '📝 ',
                      style: TextStyle(fontSize: 28),
                    ),
                    Text(
                      'Tasks',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              AnimatedBuilder(
                animation: _sparkleController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _sparkleController.value * 0.5,
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        '🔍',
                        style: TextStyle(fontSize: 20),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 25),
          // Stats Row
          Row(
            children: [
              Expanded(
                child: _buildStatCard('Completed', '24', '✅'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Pending', '8', '⏳'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Earned', '47', '💰'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, String emoji) {
    return AnimatedBuilder(
      animation: _bounceController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_bounceController.value * 0.05),
          child: Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withOpacity(0.5), width: 2),
            ),
            child: Column(
              children: [
                Text(
                  emoji,
                  style: const TextStyle(fontSize: 24),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        blurRadius: 3,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTasksList() {
    final tasks = [
      {
        'title': 'Complete 5 Links',
        'description': 'Visit and stay for 30 seconds',
        'reward': '250 🪙',
        'progress': 3,
        'total': 5,
        'color': const Color(0xFF4FACFE),
        'emoji': '🔗',
      },
      {
        'title': 'Watch 3 Videos',
        'description': 'Watch full video ads',
        'reward': '180 🪙',
        'progress': 1,
        'total': 3,
        'color': const Color(0xFFFF6B9D),
        'emoji': '📺',
      },
      {
        'title': 'Share App',
        'description': 'Share with 2 friends',
        'reward': '500 🪙',
        'progress': 0,
        'total': 2,
        'color': const Color(0xFF96CEB4),
        'emoji': '📤',
      },
      {
        'title': 'Daily Check-in',
        'description': 'Login for 7 consecutive days',
        'reward': '320 🪙',
        'progress': 4,
        'total': 7,
        'color': const Color(0xFFFFD93D),
        'emoji': '📅',
      },
      {
        'title': 'Rate App',
        'description': 'Give us 5 stars on store',
        'reward': '200 🪙',
        'progress': 0,
        'total': 1,
        'color': const Color(0xFFFF8E9B),
        'emoji': '⭐',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return AnimatedBuilder(
          animation: _bounceController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, _bounceController.value * (index % 2 == 0 ? 3 : -3)),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: _buildTaskCard(task),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTaskCard(Map<String, dynamic> task) {
    final progress = task['progress'] as int;
    final total = task['total'] as int;
    final progressPercent = progress / total;
    final isCompleted = progress >= total;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: (task['color'] as Color).withOpacity(0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: (task['color'] as Color).withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      (task['color'] as Color).withOpacity(0.2),
                      (task['color'] as Color).withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  task['emoji'] as String,
                  style: const TextStyle(fontSize: 28),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task['title'] as String,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      task['description'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      (task['color'] as Color),
                      (task['color'] as Color).withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Text(
                  task['reward'] as String,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Progress Section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress: $progress/$total 🎯',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: (task['color'] as Color).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  '${(progressPercent * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: task['color'] as Color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress Bar
          Container(
            height: 12,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(6),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progressPercent,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      (task['color'] as Color),
                      (task['color'] as Color).withOpacity(0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isCompleted ? null : () {
                // Handle task action
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isCompleted ? Colors.grey[300] : task['color'] as Color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: isCompleted ? 0 : 5,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    isCompleted ? '✅ Completed!' : '🚀 Start Task',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
