import 'package:flutter/material.dart';
import 'package:stack_appodeal_flutter/stack_appodeal_flutter.dart';

class GlobalBannerAd extends StatefulWidget {
  final String adId;
  final bool isVertical;
  final double? height;
  final double? width;

  const GlobalBannerAd({
    super.key,
    required this.adId,
    this.isVertical = false,
    this.height,
    this.width,
  });

  @override
  State<GlobalBannerAd> createState() => _GlobalBannerAdState();
}

class _GlobalBannerAdState extends State<GlobalBannerAd> {
  bool _isLoaded = false;
  bool _isLoading = true;
  static bool _isAppodealInitialized = false;

  @override
  void initState() {
    super.initState();
    _loadBanner();
  }

  Future<void> _loadBanner() async {
    try {
      // Initialize Appodeal only once globally
      if (!_isAppodealInitialized) {
        print('🚀 Initializing Appodeal for banner ads...');
        await Appodeal.initialize(
          appKey: 'fbcc85986084ac5f24921a8856198d2171247fe418b0b247',
          adTypes: [
            AppodealAdType.Banner,
            AppodealAdType.Interstitial,
            AppodealAdType.RewardedVideo,
          ],
        );
        _isAppodealInitialized = true;
        print('✅ Appodeal initialized successfully');
      }

      // Cache banner ads
      await Appodeal.cache(AppodealAdType.Banner);
      print('🔄 Caching banner ads for ${widget.adId}...');

      // Wait for ads to load
      await Future.delayed(const Duration(milliseconds: 1200));

      // Check if banner is loaded
      bool isLoaded = await Appodeal.isLoaded(AppodealAdType.Banner);
      print('📊 Banner ${widget.adId} loaded: $isLoaded');

      if (mounted) {
        setState(() {
          _isLoaded = isLoaded;
          _isLoading = false;
        });
      }

      if (!isLoaded && mounted) {
        // Retry loading after a short delay
        print('⚠️ Retrying banner load for ${widget.adId}...');
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (mounted) _loadBanner();
        });
      }
    } catch (e) {
      print('❌ Failed to load banner ${widget.adId}: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Retry loading after error
        Future.delayed(const Duration(milliseconds: 3000), () {
          if (mounted) _loadBanner();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? (widget.isVertical ? 250 : 60),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.transparent,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: _buildAdContent(),
      ),
    );
  }

  Widget _buildAdContent() {
    if (_isLoaded) {
      // Show real Appodeal banner ads
      print('🎯 Displaying real banner ad for ${widget.adId}');
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade200, width: 1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: AppodealBanner(
            adSize: widget.isVertical
                ? AppodealBannerSize.MEDIUM_RECTANGLE
                : AppodealBannerSize.BANNER,
            placement: widget.adId,
          ),
        ),
      );
    } else if (_isLoading) {
      // Show loading indicator while ad loads
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue.shade200, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2.5,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Loading Ad...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else {
      // Show retry button if ad failed to load
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200, width: 1),
        ),
        child: InkWell(
          onTap: () {
            print('🔄 Retrying banner ad load for ${widget.adId}');
            setState(() {
              _isLoading = true;
            });
            _loadBanner();
          },
          borderRadius: BorderRadius.circular(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.refresh,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                'Tap to Retry',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.orange.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}
