import 'package:flutter/material.dart';
import 'package:stack_appodeal_flutter/stack_appodeal_flutter.dart';

class GlobalBannerAd extends StatefulWidget {
  final String adId;
  final bool isVertical;
  final double? height;
  final double? width;

  const GlobalBannerAd({
    super.key,
    required this.adId,
    this.isVertical = false,
    this.height,
    this.width,
  });

  @override
  State<GlobalBannerAd> createState() => _GlobalBannerAdState();
}

class _GlobalBannerAdState extends State<GlobalBannerAd> {
  bool _isLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBanner();
  }

  Future<void> _loadBanner() async {
    try {
      // Show banner
      await Appodeal.show(AppodealAdType.BannerBottom);
      setState(() {
        _isLoaded = true;
      });
    } catch (e) {
      print('Failed to load banner ${widget.adId}: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? (widget.isVertical ? 250 : 60),
      width: widget.width ?? double.infinity,
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: _getBannerColor(),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.ads_click,
              color: Colors.grey.shade600,
              size: widget.isVertical ? 24 : 16,
            ),
            const SizedBox(height: 4),
            Text(
              widget.adId,
              style: TextStyle(
                fontSize: widget.isVertical ? 12 : 10,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (_isLoaded)
              Text(
                'Live Ad',
                style: TextStyle(
                  fontSize: 8,
                  color: Colors.green.shade600,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getBannerColor() {
    // Different colors for different ad IDs
    switch (widget.adId.toLowerCase()) {
      case 'home banner':
        return Colors.blue.shade50;
      case 'tasks banner':
        return Colors.green.shade50;
      case 'videos banner':
        return Colors.red.shade50;
      case 'games banner':
        return Colors.purple.shade50;
      case 'profile banner':
        return Colors.orange.shade50;
      case 'captcha top':
        return Colors.pink.shade50;
      case 'captcha left':
        return Colors.teal.shade50;
      case 'captcha right':
        return Colors.indigo.shade50;
      case 'captcha bottom':
        return Colors.amber.shade50;
      default:
        return Colors.grey.shade100;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
