import 'package:flutter/material.dart';
import 'package:stack_appodeal_flutter/stack_appodeal_flutter.dart';

class GlobalBannerAd extends StatefulWidget {
  final String adId;
  final bool isVertical;
  final double? height;
  final double? width;

  const GlobalBannerAd({
    super.key,
    required this.adId,
    this.isVertical = false,
    this.height,
    this.width,
  });

  @override
  State<GlobalBannerAd> createState() => _GlobalBannerAdState();
}

class _GlobalBannerAdState extends State<GlobalBannerAd> {
  bool _isLoaded = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBanner();
  }

  Future<void> _loadBanner() async {
    try {
      // Initialize Appodeal if not already done
      await Appodeal.initialize(
        appKey: 'fbcc85986084ac5f24921a8856198d2171247fe418b0b247',
        adTypes: [AppodealAdType.Banner],
      );

      // Cache banner ads with faster loading
      await Appodeal.cache(AppodealAdType.Banner);

      // Reduced wait time for faster loading
      await Future.delayed(const Duration(milliseconds: 800));

      // Check if banner is actually loaded
      bool isLoaded = await Appodeal.isLoaded(AppodealAdType.Banner);

      setState(() {
        _isLoaded = isLoaded;
        _isLoading = false;
      });

      if (!isLoaded) {
        // Retry loading after a short delay
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) _loadBanner();
        });
      }
    } catch (e) {
      print('Failed to load banner ${widget.adId}: $e');
      setState(() {
        _isLoading = false;
      });

      // Retry loading after error
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (mounted) _loadBanner();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? (widget.isVertical ? 250 : 60),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.transparent,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: _buildAdContent(),
      ),
    );
  }

  Widget _buildAdContent() {
    if (_isLoaded) {
      // Show real Appodeal banner ads with better configuration
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: AppodealBanner(
            adSize: widget.isVertical
                ? AppodealBannerSize.MEDIUM_RECTANGLE
                : AppodealBannerSize.BANNER,
            placement: widget.adId,
          ),
        ),
      );
    } else if (_isLoading) {
      // Show loading indicator while ad loads
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
        ),
      );
    } else {
      // Show empty container if ad failed to load
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
      );
    }
  }
}
