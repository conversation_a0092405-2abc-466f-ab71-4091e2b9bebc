import 'package:flutter/material.dart';
import 'package:stack_appodeal_flutter/stack_appodeal_flutter.dart';

class GlobalBannerAd extends StatefulWidget {
  const GlobalBannerAd({super.key});

  @override
  State<GlobalBannerAd> createState() => _GlobalBannerAdState();
}

class _GlobalBannerAdState extends State<GlobalBannerAd> {
  bool _isLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBanner();
  }

  Future<void> _loadBanner() async {
    try {
      // Show banner at bottom
      await Appodeal.show(AppodealAdType.BannerBottom);
      setState(() {
        _isLoaded = true;
      });
    } catch (e) {
      print('Failed to load banner: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isLoaded) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 60, // Standard banner height + padding
      width: double.infinity,
      color: Colors.transparent,
      child: const Center(
        child: Text(
          'Advertisement',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Hide banner when widget is disposed
    Appodeal.hide(AppodealAdType.BannerBottom);
    super.dispose();
  }
}
