import 'package:flutter/material.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen> with TickerProviderStateMixin {
  late AnimationController _slideController;
  
  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: babyBlue,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_rounded, color: navyBlue),
        ),
        title: const Text(
          '🏆 Leaderboard',
          style: TextStyle(
            color: navyBlue,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top 3 Podium
              _buildTopThreePodium(),
              const SizedBox(height: 32),
              
              // Your Rank
              _buildYourRank(),
              const SizedBox(height: 24),
              
              // All Rankings
              _buildAllRankings(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopThreePodium() {
    return Container(
      padding: const EdgeInsets.all(28),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [navyBlue, blueGrotto],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: navyBlue.withOpacity(0.4),
            blurRadius: 25,
            offset: const Offset(0, 12),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: goldenYellow,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.emoji_events_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Top Performers',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 26,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // 2nd Place
              Flexible(
                child: _buildPodiumCard(
                  '2',
                  'Sarah M.',
                  '47 Tasks',
                  '₹1,245',
                  85,
                  [blueGreen, lavender],
                ),
              ),
              // 1st Place
              Flexible(
                child: _buildPodiumCard(
                  '1',
                  'John D.',
                  '63 Tasks',
                  '₹1,890',
                  110,
                  [goldenYellow, sunsetOrange],
                ),
              ),
              // 3rd Place
              Flexible(
                child: _buildPodiumCard(
                  '3',
                  'Mike R.',
                  '39 Tasks',
                  '₹975',
                  65,
                  [coral, roseGold],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPodiumCard(String rank, String name, String tasks, String earnings, double height, List<Color> colors) {
    final isFirst = rank == '1';

    return Column(
      children: [
        // Crown for first place
        if (isFirst) ...[
          const Text(
            '👑',
            style: TextStyle(fontSize: 32),
          ),
          const SizedBox(height: 8),
        ],

        // Profile Circle
        Container(
          width: isFirst ? 70 : 60,
          height: isFirst ? 70 : 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: colors),
            borderRadius: BorderRadius.circular(isFirst ? 35 : 30),
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: colors[0].withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Center(
            child: Text(
              rank,
              style: TextStyle(
                fontSize: isFirst ? 28 : 24,
                fontWeight: FontWeight.w900,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Podium
        Container(
          width: isFirst ? 90 : 75,
          height: height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white.withOpacity(0.3),
                Colors.white.withOpacity(0.1),
              ],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    name,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isFirst ? 13 : 11,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: Text(
                    tasks,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isFirst ? 10 : 9,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      earnings,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: isFirst ? 10 : 9,
                        fontWeight: FontWeight.w700,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildYourRank() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: blueGrotto, width: 2),
        boxShadow: [
          BoxShadow(
            color: blueGrotto.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [blueGrotto, blueGreen]),
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Center(
              child: Text(
                '12',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Rank',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: navyBlue,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '23 Tasks Completed • ₹645 Earned',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: mintGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '↗️ +2',
              style: TextStyle(
                color: mintGreen,
                fontSize: 12,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllRankings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '📊 All Rankings',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w800,
            color: navyBlue,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: navyBlue.withOpacity(0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildRankingItem(4, 'Emma W.', 35, '₹875', [mintGreen, lavender]),
              _buildRankingItem(5, 'David L.', 32, '₹820', [coral, sunsetOrange]),
              _buildRankingItem(6, 'Lisa K.', 29, '₹745', [blueGrotto, blueGreen]),
              _buildRankingItem(7, 'Tom H.', 27, '₹695', [goldenYellow, sunsetOrange]),
              _buildRankingItem(8, 'Anna S.', 25, '₹650', [lavender, roseGold]),
              _buildRankingItem(9, 'Chris P.', 24, '₹620', [mintGreen, blueGreen]),
              _buildRankingItem(10, 'Maya J.', 22, '₹580', [coral, goldenYellow]),
              _buildRankingItem(11, 'Alex M.', 21, '₹545', [blueGrotto, lavender]),
              _buildRankingItem(12, 'You', 23, '₹645', [blueGrotto, blueGreen], isCurrentUser: true),
              _buildRankingItem(13, 'Sam R.', 19, '₹485', [sunsetOrange, roseGold]),
              _buildRankingItem(14, 'Nina T.', 18, '₹450', [mintGreen, goldenYellow]),
              _buildRankingItem(15, 'Jake B.', 17, '₹425', [coral, lavender], isLast: true),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRankingItem(int rank, String name, int tasks, String earnings, List<Color> colors, {bool isCurrentUser = false, bool isLast = false}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isCurrentUser ? blueGrotto.withOpacity(0.05) : null,
        border: !isLast ? Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.1), width: 1),
        ) : null,
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: isCurrentUser
                ? LinearGradient(colors: [blueGrotto, blueGreen])
                : LinearGradient(colors: [Colors.grey, Colors.grey.shade400]),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                rank.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: isCurrentUser ? blueGrotto : navyBlue,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$tasks Tasks Completed',
                  style: TextStyle(
                    fontSize: 14,
                    color: navyBlue.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                earnings,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: isCurrentUser ? blueGrotto : colors[0],
                ),
              ),
              const SizedBox(height: 4),
              if (isCurrentUser)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: blueGrotto.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'You',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w700,
                      color: blueGrotto,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
