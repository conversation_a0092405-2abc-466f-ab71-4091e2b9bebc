import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../../services/pocketbase_service.dart';
import '../main_screen.dart';
import 'register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _pocketBaseService = PocketBaseService();

  bool _isLoading = false;
  bool _obscurePassword = true;

  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _shimmerController;

  // 🌟 NEXT-LEVEL FUTURISTIC COLOR PALETTE 🌟
  static const Color cyberpunkPink = Color(0xFFFF0080);
  static const Color neonBlue = Color(0xFF00D4FF);
  static const Color electricPurple = Color(0xFF8B5CF6);
  static const Color holographicGreen = Color(0xFF00FF88);
  static const Color quantumOrange = Color(0xFFFF6B35);
  static const Color plasmaYellow = Color(0xFFFFD700);
  static const Color voidBlack = Color(0xFF0A0A0A);
  static const Color crystalWhite = Color(0xFFFFFFFE);
  static const Color ghostGray = Color(0xFF1A1A1A);
  static const Color laserRed = Color(0xFFFF073A);

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _rotationController = AnimationController(
      duration: const Duration(seconds: 25),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _slideController.forward();
    _fadeController.forward();
    _scaleController.forward();
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _shimmerController.repeat();
    _initializeService();
  }

  Future<void> _initializeService() async {
    await _pocketBaseService.init();
    // Check if user is already logged in
    if (_pocketBaseService.isAuthenticated) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => MainScreen()),
      );
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _shimmerController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await _pocketBaseService.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (result['success']) {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const MainScreen()),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'Login failed'),
              backgroundColor: laserRed,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              voidBlack,
              ghostGray,
              electricPurple.withOpacity(0.3),
              cyberpunkPink.withOpacity(0.2),
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // 🌟 CYBERPUNK ANIMATED BACKGROUND
              _buildCyberpunkBackground(),

              // 🔮 HOLOGRAPHIC PARTICLES
              _buildHolographicParticles(),

              // 🚀 MAIN FUTURISTIC CONTENT
              SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 60),

                      // 💎 QUANTUM HEADER
                      _buildQuantumHeader(),
                      const SizedBox(height: 60),

                      // 🔥 NEON GLASS LOGIN CARD
                      _buildNeonGlassCard(),
                      const SizedBox(height: 40),

                      // ⚡ ELECTRIC REGISTER LINK
                      _buildElectricRegisterLink(),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCyberpunkBackground() {
    return Stack(
      children: [
        // 🌟 NEON ENERGY ORBS
        Positioned(
          top: -120,
          right: -120,
          child: RotationTransition(
            turns: _rotationController,
            child: Container(
              width: 350,
              height: 350,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    cyberpunkPink.withOpacity(0.6),
                    neonBlue.withOpacity(0.4),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.4, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: cyberpunkPink.withOpacity(0.3),
                    blurRadius: 100,
                    spreadRadius: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: -180,
          left: -120,
          child: RotationTransition(
            turns: Tween(begin: 0.0, end: -1.0).animate(_rotationController),
            child: Container(
              width: 400,
              height: 400,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    holographicGreen.withOpacity(0.5),
                    electricPurple.withOpacity(0.3),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: holographicGreen.withOpacity(0.2),
                    blurRadius: 120,
                    spreadRadius: 30,
                  ),
                ],
              ),
            ),
          ),
        ),
        // 🔥 QUANTUM MESH GRADIENT
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _shimmerController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      electricPurple.withOpacity(0.2 + 0.15 * math.sin(_shimmerController.value * 2 * math.pi)),
                      neonBlue.withOpacity(0.15 + 0.1 * math.cos(_shimmerController.value * 2 * math.pi)),
                      cyberpunkPink.withOpacity(0.1 + 0.1 * math.sin(_shimmerController.value * 3 * math.pi)),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHolographicParticles() {
    return Stack(
      children: List.generate(20, (index) {
        final random = math.Random(index);
        final size = 3.0 + random.nextDouble() * 12.0;
        final left = random.nextDouble() * MediaQuery.of(context).size.width;
        final top = random.nextDouble() * MediaQuery.of(context).size.height;

        return Positioned(
          left: left,
          top: top,
          child: AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 0.3 + 0.7 * math.sin(_pulseController.value * 2 * math.pi + index * 0.5),
                child: Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: [cyberpunkPink, neonBlue, holographicGreen, electricPurple, quantumOrange][index % 5].withOpacity(0.8),
                    boxShadow: [
                      BoxShadow(
                        color: [cyberpunkPink, neonBlue, holographicGreen, electricPurple, quantumOrange][index % 5].withOpacity(0.6),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildQuantumHeader() {
    return FadeTransition(
      opacity: _fadeController,
      child: Column(
        children: [
          // 🔮 QUANTUM HOLOGRAPHIC LOGO
          Stack(
            alignment: Alignment.center,
            children: [
              // 🌟 OUTER ENERGY FIELD
              ScaleTransition(
                scale: Tween(begin: 1.0, end: 1.4).animate(
                  CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
                ),
                child: Container(
                  width: 160,
                  height: 160,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        cyberpunkPink.withOpacity(0.6),
                        neonBlue.withOpacity(0.4),
                        Colors.transparent,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: cyberpunkPink.withOpacity(0.4),
                        blurRadius: 60,
                        spreadRadius: 10,
                      ),
                    ],
                  ),
                ),
              ),
              // ⚡ ELECTRIC RING
              RotationTransition(
                turns: _rotationController,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        neonBlue.withOpacity(0.9),
                        holographicGreen.withOpacity(0.9),
                        cyberpunkPink.withOpacity(0.9),
                        electricPurple.withOpacity(0.9),
                        neonBlue.withOpacity(0.9),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: neonBlue.withOpacity(0.5),
                        blurRadius: 30,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ),
              // 💎 CORE QUANTUM CRYSTAL
              ScaleTransition(
                scale: CurvedAnimation(
                  parent: _scaleController,
                  curve: Curves.elasticOut,
                ),
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        plasmaYellow,
                        cyberpunkPink,
                        neonBlue,
                        holographicGreen,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: cyberpunkPink.withOpacity(0.8),
                        blurRadius: 40,
                        spreadRadius: 8,
                      ),
                      BoxShadow(
                        color: neonBlue.withOpacity(0.6),
                        blurRadius: 60,
                        spreadRadius: 15,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 50),

          // 🚀 QUANTUM TITLE WITH HOLOGRAPHIC EFFECT
          SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.5),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: _slideController,
              curve: Curves.easeOutCubic,
            )),
            child: AnimatedBuilder(
              animation: _shimmerController,
              builder: (context, child) {
                return ShaderMask(
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        crystalWhite,
                        cyberpunkPink,
                        neonBlue,
                        holographicGreen,
                        plasmaYellow,
                        crystalWhite,
                      ],
                      stops: [
                        0.0,
                        0.15 + 0.25 * math.sin(_shimmerController.value * 2 * math.pi),
                        0.35,
                        0.55 + 0.2 * math.cos(_shimmerController.value * 3 * math.pi),
                        0.75,
                        1.0,
                      ],
                    ).createShader(bounds);
                  },
                  child: const Text(
                    'EASY MONEY',
                    style: TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.w900,
                      color: Colors.white,
                      letterSpacing: 4.0,
                      shadows: [
                        Shadow(
                          color: Colors.black54,
                          offset: Offset(0, 6),
                          blurRadius: 12,
                        ),
                        Shadow(
                          color: Colors.cyan,
                          offset: Offset(0, 0),
                          blurRadius: 20,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),

          // 💫 FUTURISTIC SUBTITLE
          FadeTransition(
            opacity: _fadeController,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    cyberpunkPink.withOpacity(0.2),
                    neonBlue.withOpacity(0.15),
                    holographicGreen.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: neonBlue.withOpacity(0.4),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: neonBlue.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Text(
                '🚀 QUANTUM EARNING PORTAL 🚀',
                style: TextStyle(
                  fontSize: 18,
                  color: crystalWhite.withOpacity(0.95),
                  fontWeight: FontWeight.w700,
                  letterSpacing: 1.2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNeonGlassCard() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 0.3),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cyberpunkPink.withOpacity(0.15),
              neonBlue.withOpacity(0.12),
              electricPurple.withOpacity(0.08),
              voidBlack.withOpacity(0.3),
            ],
          ),
          borderRadius: BorderRadius.circular(32),
          border: Border.all(
            color: neonBlue.withOpacity(0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: cyberpunkPink.withOpacity(0.3),
              blurRadius: 50,
              offset: const Offset(0, 25),
              spreadRadius: 5,
            ),
            BoxShadow(
              color: neonBlue.withOpacity(0.2),
              blurRadius: 80,
              offset: const Offset(0, 40),
              spreadRadius: 10,
            ),
            BoxShadow(
              color: holographicGreen.withOpacity(0.1),
              blurRadius: 100,
              offset: const Offset(0, 60),
              spreadRadius: 15,
            ),
          ],
        ),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 🔥 CYBERPUNK WELCOME TEXT
              ShaderMask(
                shaderCallback: (bounds) {
                  return LinearGradient(
                    colors: [crystalWhite, cyberpunkPink, neonBlue, holographicGreen],
                  ).createShader(bounds);
                },
                child: const Text(
                  'NEURAL LINK',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                    letterSpacing: 2.0,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Initialize quantum connection to the earning matrix',
                style: TextStyle(
                  fontSize: 16,
                  color: crystalWhite.withOpacity(0.85),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.8,
                ),
              ),
              const SizedBox(height: 40),

              // 📧 QUANTUM EMAIL FIELD
              _buildQuantumTextField(
                controller: _emailController,
                label: 'Neural ID (Email)',
                icon: Icons.alternate_email_rounded,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Neural ID required for matrix access';
                  }
                  if (!value.contains('@')) {
                    return 'Invalid neural ID format';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // 🔐 QUANTUM PASSWORD FIELD
              _buildQuantumTextField(
                controller: _passwordController,
                label: 'Access Code (Password)',
                icon: Icons.security_rounded,
                obscureText: _obscurePassword,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                    color: neonBlue.withOpacity(0.8),
                  ),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    setState(() => _obscurePassword = !_obscurePassword);
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Access code required';
                  }
                  if (value.length < 6) {
                    return 'Access code must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 40),

              // 🚀 QUANTUM LOGIN BUTTON
              _buildQuantumButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuantumTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cyberpunkPink.withOpacity(0.15),
            neonBlue.withOpacity(0.12),
            electricPurple.withOpacity(0.08),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: neonBlue.withOpacity(0.6),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: cyberpunkPink.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: neonBlue.withOpacity(0.15),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        style: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: 0.5,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: neonBlue.withOpacity(0.9),
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(14),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [cyberpunkPink.withOpacity(0.6), neonBlue.withOpacity(0.6)],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: cyberpunkPink.withOpacity(0.3),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(icon, color: crystalWhite, size: 22),
          ),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24),
            borderSide: BorderSide(
              color: holographicGreen.withOpacity(0.9),
              width: 3,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24),
            borderSide: BorderSide(color: laserRed, width: 2),
          ),
          errorStyle: TextStyle(color: laserRed, fontWeight: FontWeight.w600),
          filled: true,
          fillColor: Colors.transparent,
          contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildQuantumButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cyberpunkPink,
                neonBlue,
                holographicGreen,
                electricPurple,
              ],
              stops: [
                0.0 + 0.15 * math.sin(_pulseController.value * 2 * math.pi),
                0.25 + 0.1 * math.cos(_pulseController.value * 2 * math.pi),
                0.65 + 0.1 * math.sin(_pulseController.value * 3 * math.pi),
                1.0,
              ],
            ),
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: cyberpunkPink.withOpacity(0.6),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
              BoxShadow(
                color: neonBlue.withOpacity(0.4),
                blurRadius: 50,
                offset: const Offset(0, 25),
                spreadRadius: 10,
              ),
              BoxShadow(
                color: holographicGreen.withOpacity(0.3),
                blurRadius: 70,
                offset: const Offset(0, 35),
                spreadRadius: 15,
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleLogin,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
            ),
            child: _isLoading
                ? Stack(
                    alignment: Alignment.center,
                    children: [
                      SizedBox(
                        width: 32,
                        height: 32,
                        child: CircularProgressIndicator(
                          strokeWidth: 4,
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          backgroundColor: Colors.white.withOpacity(0.3),
                        ),
                      ),
                      const Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: 18,
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.rocket_launch_rounded,
                        color: Colors.white,
                        size: 26,
                      ),
                      const SizedBox(width: 14),
                      Flexible(
                        child: Text(
                          'INITIATE NEURAL LINK',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: 1.2,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Widget _buildElectricRegisterLink() {
    return FadeTransition(
      opacity: _fadeController,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                electricPurple.withOpacity(0.2),
                cyberpunkPink.withOpacity(0.15),
                neonBlue.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: electricPurple.withOpacity(0.4),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: electricPurple.withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).push(
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) => const RegisterScreen(),
                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(1.0, 0.0),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      )),
                      child: FadeTransition(
                        opacity: animation,
                        child: child,
                      ),
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 400),
                ),
              );
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: AnimatedBuilder(
              animation: _shimmerController,
              builder: (context, child) {
                return ShaderMask(
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        crystalWhite.withOpacity(0.9),
                        cyberpunkPink,
                        neonBlue,
                        holographicGreen,
                        crystalWhite.withOpacity(0.9),
                      ],
                      stops: [
                        0.0,
                        0.2 + 0.2 * math.sin(_shimmerController.value * 2 * math.pi),
                        0.5,
                        0.8 + 0.2 * math.cos(_shimmerController.value * 2 * math.pi),
                        1.0,
                      ],
                    ).createShader(bounds);
                  },
                  child: RichText(
                    text: TextSpan(
                      text: "Need neural access? ",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.8,
                      ),
                      children: [
                        TextSpan(
                          text: 'CREATE MATRIX ID 🚀',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w900,
                            fontSize: 17,
                            letterSpacing: 1.0,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
