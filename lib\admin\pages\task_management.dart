import 'package:flutter/material.dart';

class TaskManagement extends StatefulWidget {
  const TaskManagement({super.key});

  @override
  State<TaskManagement> createState() => _TaskManagementState();
}

class _TaskManagementState extends State<TaskManagement> {
  // Admin Color Scheme
  static const Color primaryBlue = Color(0xFF1565C0);
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFF44336);

  // Mock task data
  final List<Map<String, dynamic>> _tasks = [
    {
      'id': '1',
      'title': 'Complete Tech Offers Challenge',
      'type': 'shortlink',
      'difficulty': 'easy',
      'reward': 2.50,
      'originalReward': 5.00,
      'completionGoal': 1000,
      'currentCompletions': 756,
      'active': true,
      'linksCount': 5,
      'createdAt': '2024-01-15',
    },
    {
      'id': '2',
      'title': 'Watch Video Advertisements',
      'type': 'video',
      'difficulty': 'easy',
      'reward': 1.00,
      'originalReward': 1.00,
      'completionGoal': 500,
      'currentCompletions': 423,
      'active': true,
      'linksCount': 0,
      'createdAt': '2024-01-20',
    },
    {
      'id': '3',
      'title': 'Gaming Achievement Tasks',
      'type': 'game',
      'difficulty': 'hard',
      'reward': 3.00,
      'originalReward': 3.00,
      'completionGoal': 200,
      'currentCompletions': 89,
      'active': false,
      'linksCount': 0,
      'createdAt': '2024-01-10',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with Add Task Button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Task Management',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: Color(0xFF1565C0),
              ),
            ),
            ElevatedButton.icon(
              onPressed: _showAddTaskDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: successGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.add),
              label: const Text('Add New Task'),
            ),
          ],
        ),
        const SizedBox(height: 24),
        
        // Task Stats
        _buildTaskStats(),
        const SizedBox(height: 24),
        
        // Tasks List
        Expanded(
          child: _buildTasksList(),
        ),
      ],
    );
  }

  Widget _buildTaskStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total Tasks',
            value: '47',
            icon: Icons.assignment,
            color: primaryBlue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Active Tasks',
            value: '32',
            icon: Icons.play_circle,
            color: successGreen,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Completed Today',
            value: '1,234',
            icon: Icons.check_circle,
            color: accentOrange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Total Rewards Paid',
            value: '\$3,456',
            icon: Icons.monetization_on,
            color: warningAmber,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF1565C0),
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(flex: 3, child: Text('Task', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Type', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Progress', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Reward', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Status', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Actions', style: TextStyle(fontWeight: FontWeight.w600))),
              ],
            ),
          ),
          
          // Table Body
          Expanded(
            child: ListView.builder(
              itemCount: _tasks.length,
              itemBuilder: (context, index) {
                final task = _tasks[index];
                final progress = task['currentCompletions'] / task['completionGoal'];
                
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Task Info
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              task['title'],
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                _buildDifficultyChip(task['difficulty']),
                                if (task['type'] == 'shortlink') ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: lightBlue.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      '${task['linksCount']} links',
                                      style: TextStyle(
                                        color: lightBlue,
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      // Type
                      Expanded(
                        child: _buildTypeChip(task['type']),
                      ),
                      
                      // Progress
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${task['currentCompletions']}/${task['completionGoal']}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: progress,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                progress >= 1.0 ? successGreen : primaryBlue,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Reward
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '\$${task['reward'].toStringAsFixed(2)}',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: successGreen,
                                fontSize: 14,
                              ),
                            ),
                            if (task['reward'] != task['originalReward'])
                              Text(
                                '\$${task['originalReward'].toStringAsFixed(2)}',
                                style: TextStyle(
                                  decoration: TextDecoration.lineThrough,
                                  color: Colors.grey.shade500,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                      ),
                      
                      // Status
                      Expanded(
                        child: _buildStatusChip(task['active']),
                      ),
                      
                      // Actions
                      Expanded(
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: () => _editTask(task),
                              icon: const Icon(Icons.edit),
                              color: primaryBlue,
                              tooltip: 'Edit Task',
                            ),
                            IconButton(
                              onPressed: () => _toggleTaskStatus(task),
                              icon: Icon(task['active'] ? Icons.pause : Icons.play_arrow),
                              color: task['active'] ? warningAmber : successGreen,
                              tooltip: task['active'] ? 'Pause Task' : 'Activate Task',
                            ),
                            IconButton(
                              onPressed: () => _deleteTask(task),
                              icon: const Icon(Icons.delete),
                              color: errorRed,
                              tooltip: 'Delete Task',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyChip(String difficulty) {
    Color color;
    switch (difficulty) {
      case 'easy':
        color = successGreen;
        break;
      case 'medium':
        color = warningAmber;
        break;
      case 'hard':
        color = errorRed;
        break;
      default:
        color = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        difficulty.toUpperCase(),
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTypeChip(String type) {
    IconData icon;
    Color color;
    String label;
    
    switch (type) {
      case 'shortlink':
        icon = Icons.link;
        color = primaryBlue;
        label = 'Short Link';
        break;
      case 'video':
        icon = Icons.play_circle;
        color = accentOrange;
        label = 'Video';
        break;
      case 'game':
        icon = Icons.games;
        color = successGreen;
        label = 'Game';
        break;
      default:
        icon = Icons.task;
        color = Colors.grey;
        label = 'Other';
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(bool active) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: active ? successGreen.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        active ? 'Active' : 'Paused',
        style: TextStyle(
          color: active ? successGreen : Colors.grey,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showAddTaskDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Task'),
        content: const SizedBox(
          width: 400,
          child: Text('Task creation form would go here with fields for title, type, difficulty, reward, links, etc.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Task created successfully!')),
              );
            },
            child: const Text('Create Task'),
          ),
        ],
      ),
    );
  }

  void _editTask(Map<String, dynamic> task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Task: ${task['title']}'),
        content: const SizedBox(
          width: 400,
          child: Text('Task editing form would go here with pre-filled values.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Task updated successfully!')),
              );
            },
            child: const Text('Update Task'),
          ),
        ],
      ),
    );
  }

  void _toggleTaskStatus(Map<String, dynamic> task) {
    setState(() {
      task['active'] = !task['active'];
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Task ${task['active'] ? 'activated' : 'paused'} successfully!',
        ),
        backgroundColor: task['active'] ? successGreen : warningAmber,
      ),
    );
  }

  void _deleteTask(Map<String, dynamic> task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: Text('Are you sure you want to delete "${task['title']}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _tasks.remove(task);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Task deleted successfully!'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
