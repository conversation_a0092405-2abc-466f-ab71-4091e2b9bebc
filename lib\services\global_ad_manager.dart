import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'appodeal_service.dart';

class GlobalAdManager {
  static final GlobalAdManager _instance = GlobalAdManager._internal();
  factory GlobalAdManager() => _instance;
  GlobalAdManager._internal();

  final AppodealService _appodealService = AppodealService();
  Timer? _interstitialTimer;
  DateTime? _lastInterstitialTime;
  bool _isAppActive = true;

  // Show interstitial every 5 minutes (300 seconds)
  static const int _interstitialIntervalSeconds = 300;

  /// Start the global ad timer
  void startGlobalAdTimer() {
    _stopTimer(); // Stop any existing timer
    
    _interstitialTimer = Timer.periodic(
      const Duration(seconds: _interstitialIntervalSeconds),
      (timer) {
        if (_isAppActive && _shouldShowInterstitial()) {
          _showTimedInterstitial();
        }
      },
    );
    
    log('🕐 Global ad timer started - Interstitials every 5 minutes');
  }

  /// Stop the global ad timer
  void stopGlobalAdTimer() {
    _stopTimer();
    log('🛑 Global ad timer stopped');
  }

  /// Set app active state
  void setAppActive(bool isActive) {
    _isAppActive = isActive;
    if (isActive) {
      startGlobalAdTimer();
    } else {
      stopGlobalAdTimer();
    }
  }

  /// Check if we should show interstitial
  bool _shouldShowInterstitial() {
    if (_lastInterstitialTime == null) return true;
    
    final timeSinceLastAd = DateTime.now().difference(_lastInterstitialTime!);
    return timeSinceLastAd.inSeconds >= _interstitialIntervalSeconds;
  }

  /// Show timed interstitial ad
  Future<void> _showTimedInterstitial() async {
    try {
      final isAvailable = await _appodealService.isInterstitialAvailable();
      if (isAvailable) {
        log('📱 Showing timed interstitial ad');
        await _appodealService.showInterstitial();
        _lastInterstitialTime = DateTime.now();
      } else {
        log('⚠️ Timed interstitial not available');
      }
    } catch (e) {
      log('❌ Failed to show timed interstitial: $e');
    }
  }

  /// Stop the timer
  void _stopTimer() {
    _interstitialTimer?.cancel();
    _interstitialTimer = null;
  }

  /// Dispose resources
  void dispose() {
    _stopTimer();
  }
}

/// Mixin to add global ad functionality to any widget
mixin GlobalAdMixin<T extends StatefulWidget> on State<T> {
  final GlobalAdManager _globalAdManager = GlobalAdManager();

  @override
  void initState() {
    super.initState();
    // Start global ads when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _globalAdManager.setAppActive(true);
    });
  }

  @override
  void dispose() {
    // Don't stop global ads here as other screens might be active
    super.dispose();
  }
}

/// Widget wrapper that includes global banner ad
class ScreenWithAds extends StatelessWidget {
  final Widget child;
  final bool showBanner;

  const ScreenWithAds({
    super.key,
    required this.child,
    this.showBanner = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: showBanner 
        ? Container(
            height: 60,
            color: Colors.grey.shade100,
            child: const Center(
              child: Text(
                'Advertisement Space',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ),
          )
        : null,
    );
  }
}
