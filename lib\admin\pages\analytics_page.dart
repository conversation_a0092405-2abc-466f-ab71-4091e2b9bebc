import 'package:flutter/material.dart';

class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage> {
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Analytics & Revenue Dashboard\n\n'
        '• Total ads played, tasks completed, games finished\n'
        '• Daily, weekly, monthly revenue summaries\n'
        '• Breakdown by short links, banner ads, rewarded ads\n'
        '• User engagement metrics\n'
        '• Revenue forecasting and trends',
        style: TextStyle(fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }
}
