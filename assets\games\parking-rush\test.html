<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking Rush - Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .game-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #333;
            border-radius: 10px;
            margin: 20px 0;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚗 Parking Rush - HTML5 Game</h1>
        
        <div class="instructions">
            <h3>📋 How to Play:</h3>
            <ol>
                <li>Draw lines from each colored car to its matching colored parking spot</li>
                <li>Make sure paths don't cause cars to collide</li>
                <li>Click "Start Cars" when all paths are drawn</li>
                <li>Watch cars follow your paths - avoid crashes!</li>
                <li>Complete all levels to win</li>
            </ol>
        </div>

        <iframe src="index.html" class="game-frame" frameborder="0"></iframe>

        <div class="features">
            <div class="feature">
                <h3>🎮 Game Features</h3>
                <p>Touch/mouse drawing, collision detection, smooth animations, multiple levels</p>
            </div>
            <div class="feature">
                <h3>📱 Mobile Ready</h3>
                <p>Responsive design, touch controls, full-screen support</p>
            </div>
            <div class="feature">
                <h3>🏆 Win/Lose Events</h3>
                <p>Triggers window.postMessage for Flutter WebView integration</p>
            </div>
            <div class="feature">
                <h3>🎨 Clean UI</h3>
                <p>Modern design, smooth animations, intuitive controls</p>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 Technical Details:</h3>
            <ul>
                <li><strong>Canvas-based:</strong> HTML5 Canvas for smooth rendering</li>
                <li><strong>Touch Support:</strong> Works on mobile devices</li>
                <li><strong>Collision Detection:</strong> Real-time car collision checking</li>
                <li><strong>Path Drawing:</strong> Smooth line drawing with visual feedback</li>
                <li><strong>Animation:</strong> RequestAnimationFrame for 60fps</li>
                <li><strong>Events:</strong> window.postMessage('win'/'lose') for app integration</li>
            </ul>
        </div>
    </div>
</body>
</html>
