<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="videoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667EEA;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764BA2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F093FB;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="playGradient" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Video screen -->
  <rect x="15" y="25" width="70" height="45" rx="8" ry="8" fill="url(#videoGradient)" stroke="#4A5568" stroke-width="2"/>
  
  <!-- Screen bezel -->
  <rect x="18" y="28" width="64" height="39" rx="4" ry="4" fill="#1A202C"/>
  
  <!-- Play button circle -->
  <circle cx="50" cy="47" r="12" fill="url(#playGradient)" stroke="#B8860B" stroke-width="2"/>
  
  <!-- Play triangle -->
  <polygon points="46,42 46,52 56,47" fill="#1A202C"/>
  
  <!-- Video stand -->
  <rect x="45" y="70" width="10" height="8" fill="#4A5568"/>
  <rect x="35" y="78" width="30" height="4" rx="2" ry="2" fill="#4A5568"/>
  
  <!-- Screen reflection -->
  <rect x="20" y="30" width="25" height="15" rx="2" ry="2" fill="#FFFFFF" opacity="0.1"/>
  
  <!-- Sparkles around video -->
  <polygon points="85,20 86,23 89,23 87,25 88,28 85,26 82,28 83,25 81,23 84,23" fill="#FFD700" opacity="0.8"/>
  <polygon points="10,35 11,37 13,37 12,38 13,40 10,39 7,40 8,38 7,37 9,37" fill="#FFD700" opacity="0.8"/>
  <polygon points="85,65 86,67 88,67 87,68 88,70 85,69 82,70 83,68 82,67 84,67" fill="#FFD700" opacity="0.8"/>
  
  <!-- Additional glow -->
  <circle cx="50" cy="47" r="15" fill="url(#playGradient)" opacity="0.3"/>
</svg>
