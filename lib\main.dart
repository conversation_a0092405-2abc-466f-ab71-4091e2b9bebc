import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/auth/auth_wrapper.dart';
import 'services/appodeal_service.dart';
import 'services/global_ad_manager.dart';
import 'services/pocketbase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize PocketBase service
  await pocketBaseService.init();

  // Initialize Appodeal ads
  await AppodealService().initialize();

  // Start global ad manager
  GlobalAdManager().startGlobalAdTimer();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Easy Money',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF189AB4)),
        useMaterial3: true,
        fontFamily: 'SF Pro Display',
      ),
      home: const AuthWrapper(),
      debugShowCheckedModeBanner: false,
    );
  }
}

