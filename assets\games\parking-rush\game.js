// Parking Rush Game - Main Game Logic
class ParkingRushGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentLevel = 1;
        this.score = 0;
        this.gameState = 'menu'; // menu, playing, animating, won, lost
        this.cars = [];
        this.parkingSpots = [];
        this.paths = [];
        this.isDrawing = false;
        this.currentPath = null;
        this.animationId = null;
        
        this.setupCanvas();
        this.setupEventListeners();
        this.loadLevel(this.currentLevel);
        this.hideLoadingScreen();
    }

    // Setup canvas dimensions
    setupCanvas() {
        const resizeCanvas = () => {
            const container = document.getElementById('gameContainer');
            const header = document.getElementById('gameHeader');
            const controls = document.getElementById('gameControls');
            
            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight - header.offsetHeight - controls.offsetHeight;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // Setup event listeners
    setupEventListeners() {
        // Canvas events
        this.canvas.addEventListener('mousedown', this.handleStart.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleEnd.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleEnd.bind(this));
        
        // Button events
        document.getElementById('startBtn').addEventListener('click', this.startAnimation.bind(this));
        document.getElementById('clearBtn').addEventListener('click', this.clearPaths.bind(this));
        document.getElementById('restartBtn').addEventListener('click', this.restartLevel.bind(this));
        document.getElementById('nextLevelBtn').addEventListener('click', this.nextLevel.bind(this));
        document.getElementById('retryBtn').addEventListener('click', this.restartLevel.bind(this));
    }

    // Get mouse/touch position
    getEventPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);
        
        return {
            x: clientX - rect.left,
            y: clientY - rect.top
        };
    }

    // Handle start drawing
    handleStart(e) {
        if (this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        // Check if starting from a car
        const car = this.getCarAtPosition(pos.x, pos.y);
        if (car && !this.getPathForCar(car)) {
            this.isDrawing = true;
            this.currentPath = {
                car: car,
                points: [{ x: car.x, y: car.y }],
                color: car.color
            };
        }
    }

    // Handle drawing movement
    handleMove(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            this.currentPath.points.push({ x: pos.x, y: pos.y });
            this.render();
        }
    }

    // Handle end drawing
    handleEnd(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            // Check if ending at correct parking spot
            const spot = this.getParkingSpotAtPosition(pos.x, pos.y);
            if (spot && spot.color === this.currentPath.car.color) {
                this.currentPath.target = spot;
                this.paths.push(this.currentPath);
                this.updateUI();
            }
        }
        
        this.isDrawing = false;
        this.currentPath = null;
        this.render();
    }

    // Get car at position
    getCarAtPosition(x, y) {
        return this.cars.find(car => {
            const dx = x - car.x;
            const dy = y - car.y;
            return Math.sqrt(dx * dx + dy * dy) <= car.radius;
        });
    }

    // Get parking spot at position
    getParkingSpotAtPosition(x, y) {
        return this.parkingSpots.find(spot => {
            return x >= spot.x && x <= spot.x + spot.width &&
                   y >= spot.y && y <= spot.y + spot.height;
        });
    }

    // Get path for car
    getPathForCar(car) {
        return this.paths.find(path => path.car === car);
    }

    // Update UI elements
    updateUI() {
        document.getElementById('levelText').textContent = `Level ${this.currentLevel}`;
        document.getElementById('scoreText').textContent = `Score: ${this.score}`;
        
        const allCarsHavePaths = this.cars.every(car => this.getPathForCar(car));
        document.getElementById('startBtn').disabled = !allCarsHavePaths;
    }

    // Clear all paths
    clearPaths() {
        this.paths = [];
        this.updateUI();
        this.render();
    }

    // Restart current level
    restartLevel() {
        this.loadLevel(this.currentLevel);
    }

    // Load next level
    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > this.levels.length) {
            this.showModal('🎉', 'Game Complete!', 'You completed all levels!', false);
            window.postMessage && window.postMessage('win');
            return;
        }
        this.loadLevel(this.currentLevel);
    }

    // Hide loading screen
    hideLoadingScreen() {
        setTimeout(() => {
            document.getElementById('loadingScreen').style.opacity = '0';
            setTimeout(() => {
                document.getElementById('loadingScreen').style.display = 'none';
            }, 500);
        }, 2000);
    }

    // Show modal
    showModal(icon, title, message, showNext = true) {
        document.getElementById('modalIcon').textContent = icon;
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('nextLevelBtn').style.display = showNext ? 'block' : 'none';
        document.getElementById('gameModal').classList.remove('hidden');
    }

    // Hide modal
    hideModal() {
        document.getElementById('gameModal').classList.add('hidden');
    }

    // Load level
    loadLevel(levelNum) {
        if (levelNum > this.levels.length) return;

        const level = this.levels[levelNum - 1];
        this.cars = level.cars.map(car => ({
            ...car,
            originalX: car.x,
            originalY: car.y,
            currentX: car.x,
            currentY: car.y,
            pathIndex: 0,
            isMoving: false
        }));

        this.parkingSpots = [...level.parkingSpots];
        this.paths = [];
        this.gameState = 'playing';

        this.updateUI();
        this.render();
        this.hideModal();
    }

    // Start car animation
    startAnimation() {
        if (this.paths.length !== this.cars.length) return;

        this.gameState = 'animating';
        this.cars.forEach(car => {
            car.isMoving = true;
            car.pathIndex = 0;
        });

        this.animate();
    }

    // Animation loop
    animate() {
        this.updateCarPositions();
        this.checkCollisions();
        this.checkWinCondition();
        this.render();

        if (this.gameState === 'animating') {
            this.animationId = requestAnimationFrame(() => this.animate());
        }
    }

    // Update car positions along paths
    updateCarPositions() {
        let allCarsFinished = true;

        this.cars.forEach(car => {
            if (!car.isMoving) return;

            const path = this.getPathForCar(car);
            if (!path || car.pathIndex >= path.points.length - 1) {
                car.isMoving = false;
                return;
            }

            allCarsFinished = false;

            const currentPoint = path.points[car.pathIndex];
            const nextPoint = path.points[car.pathIndex + 1];

            const dx = nextPoint.x - currentPoint.x;
            const dy = nextPoint.y - currentPoint.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 2) {
                car.pathIndex++;
                return;
            }

            const speed = 2;
            car.currentX += (dx / distance) * speed;
            car.currentY += (dy / distance) * speed;
        });

        if (allCarsFinished) {
            this.gameState = 'playing';
        }
    }

    // Check for collisions between cars
    checkCollisions() {
        for (let i = 0; i < this.cars.length; i++) {
            for (let j = i + 1; j < this.cars.length; j++) {
                const car1 = this.cars[i];
                const car2 = this.cars[j];

                if (!car1.isMoving || !car2.isMoving) continue;

                const dx = car1.currentX - car2.currentX;
                const dy = car1.currentY - car2.currentY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 30) {
                    this.gameState = 'lost';
                    this.showModal('💥', 'Collision!', 'Cars crashed! Try again.', false);
                    window.postMessage && window.postMessage('lose');
                    return;
                }
            }
        }
    }

    // Check win condition
    checkWinCondition() {
        const allCarsParked = this.cars.every(car => {
            if (car.isMoving) return false;

            const path = this.getPathForCar(car);
            if (!path || !path.target) return false;

            const spot = path.target;
            return car.currentX >= spot.x && car.currentX <= spot.x + spot.width &&
                   car.currentY >= spot.y && car.currentY <= spot.y + spot.height;
        });

        if (allCarsParked && this.gameState === 'playing') {
            this.gameState = 'won';
            this.score += 100 * this.currentLevel;
            this.showModal('🎉', 'Level Complete!', 'All cars parked safely!', true);
            window.postMessage && window.postMessage('win');
        }
    }

    // Game levels data
    get levels() {
        return [
            {
                cars: [
                    { x: 100, y: 150, color: '#ff4444', radius: 20 },
                    { x: 100, y: 250, color: '#4444ff', radius: 20 }
                ],
                parkingSpots: [
                    { x: 400, y: 130, width: 40, height: 40, color: '#ff4444' },
                    { x: 400, y: 230, width: 40, height: 40, color: '#4444ff' }
                ]
            },
            {
                cars: [
                    { x: 80, y: 120, color: '#ff4444', radius: 20 },
                    { x: 80, y: 220, color: '#44ff44', radius: 20 },
                    { x: 80, y: 320, color: '#4444ff', radius: 20 }
                ],
                parkingSpots: [
                    { x: 450, y: 300, width: 40, height: 40, color: '#ff4444' },
                    { x: 450, y: 200, width: 40, height: 40, color: '#44ff44' },
                    { x: 450, y: 100, width: 40, height: 40, color: '#4444ff' }
                ]
            },
            {
                cars: [
                    { x: 60, y: 100, color: '#ff4444', radius: 20 },
                    { x: 60, y: 200, color: '#44ff44', radius: 20 },
                    { x: 60, y: 300, color: '#4444ff', radius: 20 },
                    { x: 60, y: 400, color: '#ffff44', radius: 20 }
                ],
                parkingSpots: [
                    { x: 500, y: 380, width: 40, height: 40, color: '#ff4444' },
                    { x: 500, y: 280, width: 40, height: 40, color: '#44ff44' },
                    { x: 500, y: 180, width: 40, height: 40, color: '#4444ff' },
                    { x: 500, y: 80, width: 40, height: 40, color: '#ffff44' }
                ]
            }
        ];
    }

    // Render game
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        this.drawBackground();

        // Draw parking spots
        this.parkingSpots.forEach(spot => this.drawParkingSpot(spot));

        // Draw paths
        this.paths.forEach(path => this.drawPath(path));

        // Draw current path being drawn
        if (this.currentPath) {
            this.drawPath(this.currentPath);
        }

        // Draw cars
        this.cars.forEach(car => this.drawCar(car));
    }

    // Draw background
    drawBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
        gradient.addColorStop(0, '#f0f8ff');
        gradient.addColorStop(1, '#e6f3ff');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw grid pattern
        this.ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        this.ctx.lineWidth = 1;

        for (let x = 0; x < this.canvas.width; x += 50) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y < this.canvas.height; y += 50) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    // Draw car
    drawCar(car) {
        const x = car.currentX || car.x;
        const y = car.currentY || car.y;

        // Car shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.ellipse(x + 2, y + 2, car.radius, car.radius * 0.8, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // Car body
        this.ctx.fillStyle = car.color;
        this.ctx.beginPath();
        this.ctx.arc(x, y, car.radius, 0, Math.PI * 2);
        this.ctx.fill();

        // Car outline
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();

        // Car details (windows)
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.beginPath();
        this.ctx.arc(x, y, car.radius * 0.6, 0, Math.PI * 2);
        this.ctx.fill();

        // Car emoji
        this.ctx.fillStyle = '#333';
        this.ctx.font = `${car.radius}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('🚗', x, y);
    }

    // Draw parking spot
    drawParkingSpot(spot) {
        // Spot background
        this.ctx.fillStyle = spot.color;
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillRect(spot.x, spot.y, spot.width, spot.height);
        this.ctx.globalAlpha = 1;

        // Spot border
        this.ctx.strokeStyle = spot.color;
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([10, 5]);
        this.ctx.strokeRect(spot.x, spot.y, spot.width, spot.height);
        this.ctx.setLineDash([]);

        // Parking icon
        this.ctx.fillStyle = spot.color;
        this.ctx.font = '20px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('P', spot.x + spot.width / 2, spot.y + spot.height / 2);
    }

    // Draw path
    drawPath(path) {
        if (path.points.length < 2) return;

        this.ctx.strokeStyle = path.color;
        this.ctx.lineWidth = 4;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.globalAlpha = 0.8;

        this.ctx.beginPath();
        this.ctx.moveTo(path.points[0].x, path.points[0].y);

        for (let i = 1; i < path.points.length; i++) {
            this.ctx.lineTo(path.points[i].x, path.points[i].y);
        }

        this.ctx.stroke();
        this.ctx.globalAlpha = 1;

        // Draw arrow at end
        if (path.points.length > 1) {
            const lastPoint = path.points[path.points.length - 1];
            const secondLastPoint = path.points[path.points.length - 2];

            const angle = Math.atan2(lastPoint.y - secondLastPoint.y, lastPoint.x - secondLastPoint.x);

            this.ctx.fillStyle = path.color;
            this.ctx.save();
            this.ctx.translate(lastPoint.x, lastPoint.y);
            this.ctx.rotate(angle);

            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(-15, -8);
            this.ctx.lineTo(-15, 8);
            this.ctx.closePath();
            this.ctx.fill();

            this.ctx.restore();
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new ParkingRushGame();
});
