import 'package:flutter/material.dart';
import 'admin_dashboard.dart';
import 'admin_login.dart';
import '../services/pocketbase_service.dart';

class AdminApp extends StatelessWidget {
  const AdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Easy Money Admin Panel',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        fontFamily: 'SF Pro Display',
      ),
      home: const AdminWrapper(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AdminWrapper extends StatefulWidget {
  const AdminWrapper({super.key});

  @override
  State<AdminWrapper> createState() => _AdminWrapperState();
}

class _AdminWrapperState extends State<AdminWrapper> {
  bool _isLoading = true;
  bool _isAdminAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAdminAuth();
  }

  Future<void> _checkAdminAuth() async {
    try {
      await pocketBaseService.init();
      
      // Check if current user is admin
      final user = pocketBaseService.currentUser;
      setState(() {
        _isAdminAuthenticated = user != null && user['role'] == 'admin';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isAdminAuthenticated = false;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return _isAdminAuthenticated 
        ? const AdminDashboard() 
        : AdminLogin(onLoginSuccess: () {
            setState(() {
              _isAdminAuthenticated = true;
            });
          });
  }
}
