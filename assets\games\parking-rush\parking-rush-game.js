// Parking Rush Game - Complete Implementation with Fixed Path Following
class ParkingRushGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentLevel = 1;
        this.score = 0;
        this.gameState = 'menu'; // menu, playing, animating, won, lost
        this.cars = [];
        this.parkingSpots = [];
        this.paths = [];
        this.isDrawing = false;
        this.currentPath = null;
        this.animationId = null;
        this.carImages = {};
        this.roadPattern = null;

        this.setupCanvas();
        this.loadCarImages();
        this.createRoadPattern();
        this.setupEventListeners();
        this.showWelcomeScreen();
    }

    // Load car SVG images
    loadCarImages() {
        const carColors = ['red', 'blue', 'green', 'yellow'];
        let loadedCount = 0;

        carColors.forEach(color => {
            const img = new Image();
            img.onload = () => {
                loadedCount++;
                if (loadedCount === carColors.length) {
                    console.log('All car images loaded');
                }
            };
            img.onerror = () => {
                console.warn(`Failed to load ${color} car image, using fallback`);
                loadedCount++;
            };
            // Try to load from assets folder
            img.src = `../../${color} car.svg`;
            this.carImages[color] = img;
        });
    }

    // Create road pattern background
    createRoadPattern() {
        const patternCanvas = document.createElement('canvas');
        patternCanvas.width = 100;
        patternCanvas.height = 100;
        const patternCtx = patternCanvas.getContext('2d');

        // Dark asphalt background
        patternCtx.fillStyle = '#2c2c2c';
        patternCtx.fillRect(0, 0, 100, 100);

        // Road texture with subtle noise
        for (let i = 0; i < 200; i++) {
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const opacity = Math.random() * 0.1;
            patternCtx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
            patternCtx.fillRect(x, y, 1, 1);
        }

        // Road lines (dashed white lines)
        patternCtx.strokeStyle = '#ffffff';
        patternCtx.lineWidth = 2;
        patternCtx.setLineDash([10, 10]);

        // Horizontal lines
        patternCtx.beginPath();
        patternCtx.moveTo(0, 50);
        patternCtx.lineTo(100, 50);
        patternCtx.stroke();

        // Vertical lines
        patternCtx.beginPath();
        patternCtx.moveTo(50, 0);
        patternCtx.lineTo(50, 100);
        patternCtx.stroke();

        this.roadPattern = this.ctx.createPattern(patternCanvas, 'repeat');
    }

    // Setup canvas dimensions
    setupCanvas() {
        const resizeCanvas = () => {
            const container = document.getElementById('gameContainer');
            const header = document.getElementById('gameHeader');
            const controls = document.getElementById('gameControls');

            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight - header.offsetHeight - controls.offsetHeight;
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // Setup event listeners
    setupEventListeners() {
        // Welcome screen
        document.getElementById('startGameBtn').addEventListener('click', () => {
            this.hideWelcomeScreen();
            this.loadLevel(1);
        });

        // Canvas events
        this.canvas.addEventListener('mousedown', this.handleStart.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleEnd.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleEnd.bind(this));
        
        // Button events
        document.getElementById('startBtn').addEventListener('click', this.startAnimation.bind(this));
        document.getElementById('clearBtn').addEventListener('click', this.clearPaths.bind(this));
        document.getElementById('restartBtn').addEventListener('click', this.restartLevel.bind(this));
        document.getElementById('nextLevelBtn').addEventListener('click', this.nextLevel.bind(this));
        document.getElementById('retryBtn').addEventListener('click', this.restartLevel.bind(this));
    }

    // Show welcome screen
    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'flex';
    }

    // Hide welcome screen
    hideWelcomeScreen() {
        document.getElementById('welcomeScreen').style.opacity = '0';
        setTimeout(() => {
            document.getElementById('welcomeScreen').style.display = 'none';
        }, 500);
    }

    // Get mouse/touch position
    getEventPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);
        
        return {
            x: clientX - rect.left,
            y: clientY - rect.top
        };
    }

    // Handle start drawing
    handleStart(e) {
        if (this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        // Check if starting from a car
        const car = this.getCarAtPosition(pos.x, pos.y);
        if (car && !this.getPathForCar(car)) {
            this.isDrawing = true;
            this.currentPath = {
                car: car,
                points: [{ x: car.x, y: car.y }],
                color: car.color
            };
        }
    }

    // Handle drawing movement
    handleMove(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            this.currentPath.points.push({ x: pos.x, y: pos.y });
            this.render();
        }
    }

    // Handle end drawing
    handleEnd(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            // Check if ending at correct parking spot
            const spot = this.getParkingSpotAtPosition(pos.x, pos.y);
            if (spot && spot.color === this.currentPath.car.color) {
                this.currentPath.target = spot;
                this.paths.push(this.currentPath);
                this.updateUI();
            }
        }
        
        this.isDrawing = false;
        this.currentPath = null;
        this.render();
    }

    // Get car at position
    getCarAtPosition(x, y) {
        return this.cars.find(car => {
            const dx = x - car.x;
            const dy = y - car.y;
            return Math.sqrt(dx * dx + dy * dy) <= car.radius;
        });
    }

    // Get parking spot at position
    getParkingSpotAtPosition(x, y) {
        return this.parkingSpots.find(spot => {
            return x >= spot.x && x <= spot.x + spot.width &&
                   y >= spot.y && y <= spot.y + spot.height;
        });
    }

    // Get path for car
    getPathForCar(car) {
        return this.paths.find(path => path.car === car);
    }

    // Update UI elements
    updateUI() {
        document.getElementById('levelText').textContent = `Level ${this.currentLevel}`;
        document.getElementById('scoreText').textContent = `Score: ${this.score}`;
        
        const allCarsHavePaths = this.cars.every(car => this.getPathForCar(car));
        document.getElementById('startBtn').disabled = !allCarsHavePaths;
    }

    // Clear all paths
    clearPaths() {
        this.paths = [];
        this.updateUI();
        this.render();
    }

    // Restart current level
    restartLevel() {
        this.loadLevel(this.currentLevel);
    }

    // Load next level
    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > this.levels.length) {
            this.showModal('🎉', 'Game Complete!', 'You completed all levels! Amazing!', false);
            return;
        }
        this.loadLevel(this.currentLevel);
    }

    // Show modal
    showModal(icon, title, message, showNext = true) {
        document.getElementById('modalIcon').textContent = icon;
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('nextLevelBtn').style.display = showNext ? 'block' : 'none';
        document.getElementById('gameModal').classList.remove('hidden');
    }

    // Hide modal
    hideModal() {
        document.getElementById('gameModal').classList.add('hidden');
    }

    // Game levels data with proper car colors
    get levels() {
        return [
            {
                cars: [
                    { x: 100, y: 150, color: 'red', colorHex: '#ff0000', radius: 25, width: 50, height: 30 },
                    { x: 100, y: 250, color: 'blue', colorHex: '#0066ff', radius: 25, width: 50, height: 30 }
                ],
                parkingSpots: [
                    { x: 500, y: 130, width: 60, height: 80, color: 'red', colorHex: '#ff0000' },
                    { x: 500, y: 230, width: 60, height: 80, color: 'blue', colorHex: '#0066ff' }
                ]
            },
            {
                cars: [
                    { x: 80, y: 120, color: 'red', colorHex: '#ff0000', radius: 25, width: 50, height: 30 },
                    { x: 80, y: 220, color: 'green', colorHex: '#00cc00', radius: 25, width: 50, height: 30 },
                    { x: 80, y: 320, color: 'blue', colorHex: '#0066ff', radius: 25, width: 50, height: 30 }
                ],
                parkingSpots: [
                    { x: 520, y: 300, width: 60, height: 80, color: 'red', colorHex: '#ff0000' },
                    { x: 520, y: 200, width: 60, height: 80, color: 'green', colorHex: '#00cc00' },
                    { x: 520, y: 100, width: 60, height: 80, color: 'blue', colorHex: '#0066ff' }
                ]
            },
            {
                cars: [
                    { x: 60, y: 100, color: 'red', colorHex: '#ff0000', radius: 25, width: 50, height: 30 },
                    { x: 60, y: 200, color: 'green', colorHex: '#00cc00', radius: 25, width: 50, height: 30 },
                    { x: 60, y: 300, color: 'blue', colorHex: '#0066ff', radius: 25, width: 50, height: 30 },
                    { x: 60, y: 400, color: 'yellow', colorHex: '#ffcc00', radius: 25, width: 50, height: 30 }
                ],
                parkingSpots: [
                    { x: 550, y: 380, width: 60, height: 80, color: 'red', colorHex: '#ff0000' },
                    { x: 550, y: 280, width: 60, height: 80, color: 'green', colorHex: '#00cc00' },
                    { x: 550, y: 180, width: 60, height: 80, color: 'blue', colorHex: '#0066ff' },
                    { x: 550, y: 80, width: 60, height: 80, color: 'yellow', colorHex: '#ffcc00' }
                ]
            }
        ];
    }

    // Load level
    loadLevel(levelNum) {
        if (levelNum > this.levels.length) return;

        const level = this.levels[levelNum - 1];
        this.cars = level.cars.map(car => ({
            ...car,
            originalX: car.x,
            originalY: car.y,
            currentX: car.x,
            currentY: car.y,
            pathIndex: 0,
            isMoving: false,
            rotation: 0 // Add rotation for car direction
        }));

        this.parkingSpots = [...level.parkingSpots];
        this.paths = [];
        this.gameState = 'playing';

        this.updateUI();
        this.render();
        this.hideModal();
    }

    // Start car animation
    startAnimation() {
        if (this.paths.length !== this.cars.length) return;

        this.gameState = 'animating';
        this.cars.forEach(car => {
            car.isMoving = true;
            car.pathIndex = 0;
        });

        this.animate();
    }

    // Animation loop
    animate() {
        this.updateCarPositions();
        this.checkCollisions();
        this.checkWinCondition();
        this.render();

        if (this.gameState === 'animating') {
            this.animationId = requestAnimationFrame(() => this.animate());
        }
    }

    // Update car positions along paths - FIXED VERSION
    updateCarPositions() {
        let allCarsFinished = true;

        this.cars.forEach(car => {
            if (!car.isMoving) return;

            const path = this.getPathForCar(car);
            if (!path || !path.points || path.points.length === 0) {
                car.isMoving = false;
                return;
            }

            // If we've reached the end of the path
            if (car.pathIndex >= path.points.length - 1) {
                car.isMoving = false;
                // Set final position to the last point
                const lastPoint = path.points[path.points.length - 1];
                car.currentX = lastPoint.x;
                car.currentY = lastPoint.y;
                return;
            }

            allCarsFinished = false;

            // Get current target point
            const targetPoint = path.points[car.pathIndex + 1];

            // Calculate direction to target
            const dx = targetPoint.x - car.currentX;
            const dy = targetPoint.y - car.currentY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // If we're close enough to the target point, move to next point
            if (distance < 3) {
                car.pathIndex++;
                car.currentX = targetPoint.x;
                car.currentY = targetPoint.y;
                return;
            }

            // Move towards target point
            const speed = 1.5; // Slower speed for better accuracy
            const moveX = (dx / distance) * speed;
            const moveY = (dy / distance) * speed;

            car.currentX += moveX;
            car.currentY += moveY;

            // Calculate rotation angle for car
            car.rotation = Math.atan2(dy, dx);
        });

        if (allCarsFinished) {
            this.gameState = 'playing';
        }
    }

    // Check for collisions between cars
    checkCollisions() {
        for (let i = 0; i < this.cars.length; i++) {
            for (let j = i + 1; j < this.cars.length; j++) {
                const car1 = this.cars[i];
                const car2 = this.cars[j];

                if (!car1.isMoving || !car2.isMoving) continue;

                const dx = car1.currentX - car2.currentX;
                const dy = car1.currentY - car2.currentY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // Use car dimensions for more accurate collision
                const minDistance = Math.max(car1.width, car1.height, car2.width, car2.height) * 0.8;

                if (distance < minDistance) {
                    this.gameState = 'lost';
                    this.showModal('💥', 'Collision!', 'Cars crashed! Try drawing different paths.', false);
                    return;
                }
            }
        }
    }

    // Check win condition
    checkWinCondition() {
        const allCarsParked = this.cars.every(car => {
            if (car.isMoving) return false;

            const path = this.getPathForCar(car);
            if (!path || !path.target) return false;

            const spot = path.target;
            // Check if car center is within parking spot with some tolerance
            const tolerance = 20;
            return car.currentX >= spot.x - tolerance &&
                   car.currentX <= spot.x + spot.width + tolerance &&
                   car.currentY >= spot.y - tolerance &&
                   car.currentY <= spot.y + spot.height + tolerance;
        });

        if (allCarsParked && this.gameState === 'playing') {
            this.gameState = 'won';
            this.score += 100 * this.currentLevel;
            this.showModal('🎉', 'Level Complete!', `Great job! All cars parked safely. +${100 * this.currentLevel} points!`, true);
        }
    }

    // Render game
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        this.drawBackground();

        // Draw parking spots
        this.parkingSpots.forEach(spot => this.drawParkingSpot(spot));

        // Draw paths
        this.paths.forEach(path => this.drawPath(path));

        // Draw current path being drawn
        if (this.currentPath) {
            this.drawPath(this.currentPath);
        }

        // Draw cars
        this.cars.forEach(car => this.drawCar(car));
    }

    // Draw road background
    drawBackground() {
        // Fill with road pattern
        if (this.roadPattern) {
            this.ctx.fillStyle = this.roadPattern;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        } else {
            // Fallback to dark asphalt color
            this.ctx.fillStyle = '#2c2c2c';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }

        // Add some road markings
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([20, 20]);

        // Center line
        this.ctx.beginPath();
        this.ctx.moveTo(this.canvas.width / 2, 0);
        this.ctx.lineTo(this.canvas.width / 2, this.canvas.height);
        this.ctx.stroke();

        // Horizontal center line
        this.ctx.beginPath();
        this.ctx.moveTo(0, this.canvas.height / 2);
        this.ctx.lineTo(this.canvas.width, this.canvas.height / 2);
        this.ctx.stroke();

        this.ctx.setLineDash([]); // Reset line dash
    }

    // Draw car with SVG or fallback
    drawCar(car) {
        const x = car.currentX || car.x;
        const y = car.currentY || car.y;

        this.ctx.save();

        // Move to car position
        this.ctx.translate(x, y);

        // Rotate car based on movement direction
        if (car.rotation !== undefined) {
            this.ctx.rotate(car.rotation);
        }

        // Try to draw SVG image first
        const carImage = this.carImages[car.color];
        if (carImage && carImage.complete && carImage.naturalWidth > 0) {
            // Draw car shadow
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            this.ctx.fillRect(-car.width/2 + 2, -car.height/2 + 2, car.width, car.height);

            // Draw car SVG
            this.ctx.drawImage(carImage, -car.width/2, -car.height/2, car.width, car.height);
        } else {
            // Fallback: Draw rectangular car with color
            // Car shadow
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            this.ctx.fillRect(-car.width/2 + 2, -car.height/2 + 2, car.width, car.height);

            // Car body
            this.ctx.fillStyle = car.colorHex || car.color;
            this.ctx.fillRect(-car.width/2, -car.height/2, car.width, car.height);

            // Car outline
            this.ctx.strokeStyle = '#000';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(-car.width/2, -car.height/2, car.width, car.height);

            // Car windows
            this.ctx.fillStyle = 'rgba(200, 220, 255, 0.8)';
            this.ctx.fillRect(-car.width/2 + 5, -car.height/2 + 3, car.width - 10, car.height - 6);

            // Car direction indicator (front bumper)
            this.ctx.fillStyle = '#333';
            this.ctx.fillRect(car.width/2 - 3, -car.height/2 + 8, 3, car.height - 16);
        }

        this.ctx.restore();
    }

    // Draw parking spot like in reference image
    drawParkingSpot(spot) {
        // Parking spot background (darker road area)
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.fillRect(spot.x, spot.y, spot.width, spot.height);

        // Parking spot border (colored rectangle)
        this.ctx.strokeStyle = spot.colorHex || spot.color;
        this.ctx.lineWidth = 4;
        this.ctx.strokeRect(spot.x, spot.y, spot.width, spot.height);

        // Inner colored area (semi-transparent)
        this.ctx.fillStyle = spot.colorHex || spot.color;
        this.ctx.globalAlpha = 0.2;
        this.ctx.fillRect(spot.x + 2, spot.y + 2, spot.width - 4, spot.height - 4);
        this.ctx.globalAlpha = 1;

        // Parking symbol in center
        this.ctx.save();
        this.ctx.translate(spot.x + spot.width/2, spot.y + spot.height/2);

        // Draw "P" symbol
        this.ctx.fillStyle = spot.colorHex || spot.color;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('P', 0, 0);

        // Draw circle around P
        this.ctx.strokeStyle = spot.colorHex || spot.color;
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, 18, 0, Math.PI * 2);
        this.ctx.stroke();

        this.ctx.restore();
    }

    // Draw path with better visibility on dark road
    drawPath(path) {
        if (path.points.length < 2) return;

        // Draw path outline (white/light)
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 8;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';

        this.ctx.beginPath();
        this.ctx.moveTo(path.points[0].x, path.points[0].y);

        for (let i = 1; i < path.points.length; i++) {
            this.ctx.lineTo(path.points[i].x, path.points[i].y);
        }

        this.ctx.stroke();

        // Draw path color (inner line)
        this.ctx.strokeStyle = path.car.colorHex || path.color;
        this.ctx.lineWidth = 4;

        this.ctx.beginPath();
        this.ctx.moveTo(path.points[0].x, path.points[0].y);

        for (let i = 1; i < path.points.length; i++) {
            this.ctx.lineTo(path.points[i].x, path.points[i].y);
        }

        this.ctx.stroke();

        // Draw arrow at end
        if (path.points.length > 1) {
            const lastPoint = path.points[path.points.length - 1];
            const secondLastPoint = path.points[path.points.length - 2];

            const angle = Math.atan2(lastPoint.y - secondLastPoint.y, lastPoint.x - secondLastPoint.x);

            this.ctx.save();
            this.ctx.translate(lastPoint.x, lastPoint.y);
            this.ctx.rotate(angle);

            // Arrow outline
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.beginPath();
            this.ctx.moveTo(2, 0);
            this.ctx.lineTo(-18, -10);
            this.ctx.lineTo(-18, 10);
            this.ctx.closePath();
            this.ctx.fill();

            // Arrow color
            this.ctx.fillStyle = path.car.colorHex || path.color;
            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(-15, -8);
            this.ctx.lineTo(-15, 8);
            this.ctx.closePath();
            this.ctx.fill();

            this.ctx.restore();
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new ParkingRushGame();
});
