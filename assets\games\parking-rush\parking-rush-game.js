// Parking Rush Game - Complete Implementation with Fixed Path Following
class ParkingRushGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentLevel = 1;
        this.score = 0;
        this.gameState = 'menu'; // menu, playing, animating, won, lost
        this.cars = [];
        this.parkingSpots = [];
        this.paths = [];
        this.isDrawing = false;
        this.currentPath = null;
        this.animationId = null;
        this.carImages = {};
        this.roadPattern = null;

        this.setupCanvas();
        this.loadCarImages();
        this.createRoadPattern();
        this.setupEventListeners();
        this.showWelcomeScreen();
    }

    // Load car SVG images
    loadCarImages() {
        const carColors = ['red', 'blue', 'green', 'yellow'];
        let loadedCount = 0;

        carColors.forEach(color => {
            const img = new Image();
            img.onload = () => {
                loadedCount++;
                console.log(`✅ ${color} car SVG loaded successfully`);
                if (loadedCount === carColors.length) {
                    console.log('🎉 All car images loaded successfully!');
                }
            };
            img.onerror = () => {
                console.warn(`❌ Failed to load ${color} car SVG, using fallback graphics`);
                loadedCount++;
            };
            // Try multiple paths for car SVGs
            img.src = `../../${color} car.svg`;
            this.carImages[color] = img;

            // Also try alternative path
            if (!img.complete) {
                setTimeout(() => {
                    if (!img.complete) {
                        img.src = `../../../${color} car.svg`;
                    }
                }, 1000);
            }
        });
    }

    // Create realistic road background like reference image
    createRoadPattern() {
        // We'll draw the road directly in drawBackground() method
        // No pattern needed - just solid asphalt color
        this.roadPattern = null;
    }

    // Setup canvas dimensions
    setupCanvas() {
        const resizeCanvas = () => {
            const container = document.getElementById('gameContainer');
            const header = document.getElementById('gameHeader');
            const controls = document.getElementById('gameControls');

            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight - header.offsetHeight - controls.offsetHeight;
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // Setup event listeners
    setupEventListeners() {
        // Welcome screen
        document.getElementById('startGameBtn').addEventListener('click', () => {
            this.hideWelcomeScreen();
            this.loadLevel(1);
        });

        // Canvas events
        this.canvas.addEventListener('mousedown', this.handleStart.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleEnd.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleEnd.bind(this));
        
        // Button events
        document.getElementById('startBtn').addEventListener('click', this.startAnimation.bind(this));
        document.getElementById('clearBtn').addEventListener('click', this.clearPaths.bind(this));
        document.getElementById('restartBtn').addEventListener('click', this.restartLevel.bind(this));
        document.getElementById('nextLevelBtn').addEventListener('click', this.nextLevel.bind(this));
        document.getElementById('retryBtn').addEventListener('click', this.restartLevel.bind(this));
    }

    // Show welcome screen
    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'flex';
    }

    // Hide welcome screen
    hideWelcomeScreen() {
        document.getElementById('welcomeScreen').style.opacity = '0';
        setTimeout(() => {
            document.getElementById('welcomeScreen').style.display = 'none';
        }, 500);
    }

    // Get mouse/touch position
    getEventPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);
        
        return {
            x: clientX - rect.left,
            y: clientY - rect.top
        };
    }

    // Handle start drawing
    handleStart(e) {
        if (this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        // Check if starting from a car
        const car = this.getCarAtPosition(pos.x, pos.y);
        if (car && !this.getPathForCar(car)) {
            this.isDrawing = true;
            this.currentPath = {
                car: car,
                points: [{ x: car.x, y: car.y }],
                color: car.color
            };
        }
    }

    // Handle drawing movement
    handleMove(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            this.currentPath.points.push({ x: pos.x, y: pos.y });
            this.render();
        }
    }

    // Handle end drawing
    handleEnd(e) {
        if (!this.isDrawing || this.gameState !== 'playing') return;
        
        e.preventDefault();
        const pos = this.getEventPos(e);
        
        if (this.currentPath) {
            // Check if ending at correct parking spot
            const spot = this.getParkingSpotAtPosition(pos.x, pos.y);
            if (spot && spot.color === this.currentPath.car.color) {
                this.currentPath.target = spot;
                this.paths.push(this.currentPath);
                this.updateUI();
            }
        }
        
        this.isDrawing = false;
        this.currentPath = null;
        this.render();
    }

    // Get car at position
    getCarAtPosition(x, y) {
        return this.cars.find(car => {
            const dx = x - car.x;
            const dy = y - car.y;
            return Math.sqrt(dx * dx + dy * dy) <= car.radius;
        });
    }

    // Get parking spot at position
    getParkingSpotAtPosition(x, y) {
        return this.parkingSpots.find(spot => {
            return x >= spot.x && x <= spot.x + spot.width &&
                   y >= spot.y && y <= spot.y + spot.height;
        });
    }

    // Get path for car
    getPathForCar(car) {
        return this.paths.find(path => path.car === car);
    }

    // Update UI elements
    updateUI() {
        document.getElementById('levelText').textContent = `Level ${this.currentLevel}`;
        document.getElementById('scoreText').textContent = `Score: ${this.score}`;
        
        const allCarsHavePaths = this.cars.every(car => this.getPathForCar(car));
        document.getElementById('startBtn').disabled = !allCarsHavePaths;
    }

    // Clear all paths
    clearPaths() {
        this.paths = [];
        this.updateUI();
        this.render();
    }

    // Restart current level
    restartLevel() {
        this.loadLevel(this.currentLevel);
    }

    // Load next level
    nextLevel() {
        this.currentLevel++;
        if (this.currentLevel > this.levels.length) {
            this.showModal('🎉', 'Game Complete!', 'You completed all levels! Amazing!', false);
            return;
        }
        this.loadLevel(this.currentLevel);
    }

    // Show modal
    showModal(icon, title, message, showNext = true) {
        document.getElementById('modalIcon').textContent = icon;
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('nextLevelBtn').style.display = showNext ? 'block' : 'none';
        document.getElementById('gameModal').classList.remove('hidden');
    }

    // Hide modal
    hideModal() {
        document.getElementById('gameModal').classList.add('hidden');
    }

    // Game levels data - FIXED DIRECTIONS AND INCREASED SPACING
    get levels() {
        return [
            {
                cars: [
                    { x: 120, y: 150, color: 'red', colorHex: '#dc143c', radius: 60, width: 120, height: 80, needsRotation: true },
                    { x: 120, y: 350, color: 'blue', colorHex: '#1e90ff', radius: 60, width: 120, height: 80, needsRotation: false }
                ],
                parkingSpots: [
                    { x: 600, y: 130, width: 100, height: 80, color: 'red', colorHex: '#dc143c' },
                    { x: 600, y: 330, width: 100, height: 80, color: 'blue', colorHex: '#1e90ff' }
                ]
            },
            {
                cars: [
                    { x: 100, y: 100, color: 'red', colorHex: '#dc143c', radius: 60, width: 120, height: 80, needsRotation: true },
                    { x: 100, y: 280, color: 'green', colorHex: '#32cd32', radius: 60, width: 120, height: 80, needsRotation: true },
                    { x: 100, y: 460, color: 'blue', colorHex: '#1e90ff', radius: 60, width: 120, height: 80, needsRotation: false }
                ],
                parkingSpots: [
                    { x: 650, y: 440, width: 100, height: 80, color: 'red', colorHex: '#dc143c' },
                    { x: 650, y: 260, width: 100, height: 80, color: 'green', colorHex: '#32cd32' },
                    { x: 650, y: 80, width: 100, height: 80, color: 'blue', colorHex: '#1e90ff' }
                ]
            },
            {
                cars: [
                    { x: 80, y: 60, color: 'red', colorHex: '#dc143c', radius: 60, width: 120, height: 80, needsRotation: true },
                    { x: 80, y: 200, color: 'green', colorHex: '#32cd32', radius: 60, width: 120, height: 80, needsRotation: true },
                    { x: 80, y: 340, color: 'blue', colorHex: '#1e90ff', radius: 60, width: 120, height: 80, needsRotation: false },
                    { x: 80, y: 480, color: 'yellow', colorHex: '#ffd700', radius: 60, width: 120, height: 80, needsRotation: true }
                ],
                parkingSpots: [
                    { x: 700, y: 460, width: 100, height: 80, color: 'red', colorHex: '#dc143c' },
                    { x: 700, y: 320, width: 100, height: 80, color: 'green', colorHex: '#32cd32' },
                    { x: 700, y: 180, width: 100, height: 80, color: 'blue', colorHex: '#1e90ff' },
                    { x: 700, y: 40, width: 100, height: 80, color: 'yellow', colorHex: '#ffd700' }
                ]
            }
        ];
    }

    // Load level
    loadLevel(levelNum) {
        if (levelNum > this.levels.length) return;

        const level = this.levels[levelNum - 1];
        this.cars = level.cars.map(car => ({
            ...car,
            originalX: car.x,
            originalY: car.y,
            currentX: car.x,
            currentY: car.y,
            pathIndex: 0,
            isMoving: false,
            rotation: car.initialRotation || 0 // Start with initial rotation
        }));

        this.parkingSpots = [...level.parkingSpots];
        this.paths = [];
        this.gameState = 'playing';

        this.updateUI();
        this.render();
        this.hideModal();
    }

    // Start car animation
    startAnimation() {
        if (this.paths.length !== this.cars.length) return;

        this.gameState = 'animating';
        this.cars.forEach(car => {
            car.isMoving = true;
            car.pathIndex = 0;
        });

        this.animate();
    }

    // Animation loop
    animate() {
        this.updateCarPositions();
        this.checkCollisions();
        this.checkWinCondition();
        this.render();

        if (this.gameState === 'animating') {
            this.animationId = requestAnimationFrame(() => this.animate());
        }
    }

    // Update car positions along paths - FIXED VERSION
    updateCarPositions() {
        let allCarsFinished = true;

        this.cars.forEach(car => {
            if (!car.isMoving) return;

            const path = this.getPathForCar(car);
            if (!path || !path.points || path.points.length === 0) {
                car.isMoving = false;
                return;
            }

            // If we've reached the end of the path
            if (car.pathIndex >= path.points.length - 1) {
                car.isMoving = false;
                // Set final position to the last point
                const lastPoint = path.points[path.points.length - 1];
                car.currentX = lastPoint.x;
                car.currentY = lastPoint.y;
                return;
            }

            allCarsFinished = false;

            // Get current target point
            const targetPoint = path.points[car.pathIndex + 1];

            // Calculate direction to target
            const dx = targetPoint.x - car.currentX;
            const dy = targetPoint.y - car.currentY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // If we're close enough to the target point, move to next point
            if (distance < 5) {
                car.pathIndex++;
                car.currentX = targetPoint.x;
                car.currentY = targetPoint.y;
                return;
            }

            // Move towards target point - SMOOTHER MOVEMENT
            const speed = 1.0; // Slower speed for smoother movement, less shaking
            const moveX = (dx / distance) * speed;
            const moveY = (dy / distance) * speed;

            car.currentX += moveX;
            car.currentY += moveY;

            // Calculate rotation angle - different for red/green cars
            if (car.needsRotation) {
                car.rotation = Math.atan2(dy, dx) - Math.PI / 2; // Adjust for red/green
            } else {
                car.rotation = Math.atan2(dy, dx); // Normal for blue/yellow
            }
        });

        if (allCarsFinished) {
            this.gameState = 'playing';
        }
    }

    // Check for collisions between cars
    checkCollisions() {
        for (let i = 0; i < this.cars.length; i++) {
            for (let j = i + 1; j < this.cars.length; j++) {
                const car1 = this.cars[i];
                const car2 = this.cars[j];

                if (!car1.isMoving || !car2.isMoving) continue;

                const dx = car1.currentX - car2.currentX;
                const dy = car1.currentY - car2.currentY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // Use car dimensions for more accurate collision with bigger cars
                const minDistance = Math.max(car1.width, car1.height, car2.width, car2.height) * 0.9;

                if (distance < minDistance) {
                    this.gameState = 'lost';
                    this.showModal('💥', 'Collision!', 'Cars crashed! Try drawing different paths.', false);
                    return;
                }
            }
        }
    }

    // Check win condition
    checkWinCondition() {
        const allCarsParked = this.cars.every(car => {
            if (car.isMoving) return false;

            const path = this.getPathForCar(car);
            if (!path || !path.target) return false;

            const spot = path.target;
            // Check if car center is within parking spot with some tolerance
            const tolerance = 20;
            return car.currentX >= spot.x - tolerance &&
                   car.currentX <= spot.x + spot.width + tolerance &&
                   car.currentY >= spot.y - tolerance &&
                   car.currentY <= spot.y + spot.height + tolerance;
        });

        if (allCarsParked && this.gameState === 'playing') {
            this.gameState = 'won';
            this.score += 100 * this.currentLevel;
            this.showModal('🎉', 'Level Complete!', `Great job! All cars parked safely. +${100 * this.currentLevel} points!`, true);
        }
    }

    // Render game
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        this.drawBackground();

        // Draw parking spots
        this.parkingSpots.forEach(spot => this.drawParkingSpot(spot));

        // Draw paths
        this.paths.forEach(path => this.drawPath(path));

        // Draw current path being drawn
        if (this.currentPath) {
            this.drawPath(this.currentPath);
        }

        // Draw cars
        this.cars.forEach(car => this.drawCar(car));
    }

    // Draw realistic road background exactly like reference image
    drawBackground() {
        // Dark gray asphalt road like in reference image
        this.ctx.fillStyle = '#3a3a3a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Add subtle road texture (tiny random dots for asphalt texture)
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.02)';
        for (let i = 0; i < 500; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            this.ctx.fillRect(x, y, 1, 1);
        }

        // Add darker spots for more realistic asphalt
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        for (let i = 0; i < 200; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = Math.random() * 3 + 1;
            this.ctx.fillRect(x, y, size, size);
        }

        // NO ROAD LINES - Keep it clean like reference image
        // The reference image shows a clean asphalt surface without markings
    }

    // Draw car - ALL CARS SAME SIZE AND ORIENTATION AS BLUE CAR
    drawCar(car) {
        const x = car.currentX || car.x;
        const y = car.currentY || car.y;

        this.ctx.save();

        // Move to car position
        this.ctx.translate(x, y);

        // Rotation - different for red/green vs blue/yellow cars
        let rotationAngle = 0;
        if (car.rotation !== undefined && car.isMoving) {
            if (car.needsRotation) {
                rotationAngle = car.rotation + Math.PI / 2; // Rotate red/green cars
            } else {
                rotationAngle = car.rotation; // Blue/yellow cars normal
            }
        } else {
            // Static rotation for initial positioning
            if (car.needsRotation) {
                rotationAngle = Math.PI / 2; // 90 degrees for red/green
            }
        }

        this.ctx.rotate(rotationAngle);

        // Try to draw SVG image first
        const carImage = this.carImages[car.color];
        if (carImage && carImage.complete && carImage.naturalWidth > 0) {
            // Draw car shadow first
            this.ctx.globalAlpha = 0.4;
            this.ctx.fillStyle = '#000000';
            this.ctx.fillRect(-car.width/2 + 3, -car.height/2 + 3, car.width, car.height);
            this.ctx.globalAlpha = 1.0;

            // Draw car SVG - SAME SIZE FOR ALL CARS
            this.ctx.drawImage(carImage, -car.width/2, -car.height/2, car.width, car.height);
        } else {
            // Fallback: Draw detailed car shape - SAME SIZE FOR ALL
            this.drawFallbackCar(car);
        }

        this.ctx.restore();
    }

    // Draw bigger fallback car that matches screenshot size
    drawFallbackCar(car) {
        const w = car.width;
        const h = car.height;

        // Car shadow
        this.ctx.globalAlpha = 0.4;
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(-w/2 + 4, -h/2 + 4, w, h);
        this.ctx.globalAlpha = 1.0;

        // Main car body - bigger and more visible
        this.ctx.fillStyle = car.colorHex;
        this.ctx.fillRect(-w/2, -h/2, w, h);

        // Car outline - thicker
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(-w/2, -h/2, w, h);

        // Front windshield - bigger
        this.ctx.fillStyle = 'rgba(200, 220, 255, 0.9)';
        this.ctx.fillRect(-w/2 + 8, -h/2 + 8, w - 16, h/3);

        // Rear windshield - bigger
        this.ctx.fillRect(-w/2 + 8, h/2 - h/3 - 8, w - 16, h/3);

        // Side windows - bigger
        this.ctx.fillRect(-w/2 + 8, -h/2 + h/3 + 4, w/3, h/3 - 8);
        this.ctx.fillRect(w/2 - w/3 - 8, -h/2 + h/3 + 4, w/3, h/3 - 8);

        // Front bumper indicator - more visible
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(w/2 - 4, -h/2 + 15, 4, h - 30);

        // Headlights - bigger
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(w/2 - 2, -h/2 + 8, 2, 12);
        this.ctx.fillRect(w/2 - 2, h/2 - 20, 2, 12);

        // Add car details for better visibility
        this.ctx.fillStyle = '#222222';
        this.ctx.fillRect(-w/2 + 4, -h/2 + 4, w - 8, 4); // Top detail
        this.ctx.fillRect(-w/2 + 4, h/2 - 8, w - 8, 4); // Bottom detail
    }

    // Draw parking spot EXACTLY like reference image
    drawParkingSpot(spot) {
        // Parking spot background - slightly darker than road
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.fillRect(spot.x, spot.y, spot.width, spot.height);

        // Thick colored border like reference image
        this.ctx.strokeStyle = spot.colorHex;
        this.ctx.lineWidth = 6;
        this.ctx.strokeRect(spot.x, spot.y, spot.width, spot.height);

        // Center circle with P symbol like reference image
        this.ctx.save();
        this.ctx.translate(spot.x + spot.width/2, spot.y + spot.height/2);

        // Draw colored circle background
        this.ctx.fillStyle = spot.colorHex;
        this.ctx.beginPath();
        this.ctx.arc(0, 0, 25, 0, Math.PI * 2);
        this.ctx.fill();

        // Draw "P" symbol in white
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 28px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('P', 0, 0);

        this.ctx.restore();
    }

    // Draw path with clean style like reference image
    drawPath(path) {
        if (path.points.length < 2) return;

        // Draw single clean line with car color
        this.ctx.strokeStyle = path.car.colorHex || path.color;
        this.ctx.lineWidth = 5;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.globalAlpha = 0.9;

        this.ctx.beginPath();
        this.ctx.moveTo(path.points[0].x, path.points[0].y);

        for (let i = 1; i < path.points.length; i++) {
            this.ctx.lineTo(path.points[i].x, path.points[i].y);
        }

        this.ctx.stroke();
        this.ctx.globalAlpha = 1.0;

        // Draw simple arrow at end
        if (path.points.length > 1) {
            const lastPoint = path.points[path.points.length - 1];
            const secondLastPoint = path.points[path.points.length - 2];

            const angle = Math.atan2(lastPoint.y - secondLastPoint.y, lastPoint.x - secondLastPoint.x);

            this.ctx.save();
            this.ctx.translate(lastPoint.x, lastPoint.y);
            this.ctx.rotate(angle);

            // Simple arrow
            this.ctx.fillStyle = path.car.colorHex || path.color;
            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(-12, -6);
            this.ctx.lineTo(-12, 6);
            this.ctx.closePath();
            this.ctx.fill();

            this.ctx.restore();
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new ParkingRushGame();
});
