class User {
  final String id;
  final String email;
  final String name;
  final DateTime created;
  final DateTime updated;

  User({
    required this.id,
    required this.email,
    required this.name,
    required this.created,
    required this.updated,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'] ?? '',
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }
}

class Task {
  final String id;
  final String title;
  final String type;
  final double reward;
  final bool active;
  final DateTime? expiresAt;
  final DateTime created;
  final DateTime updated;

  Task({
    required this.id,
    required this.title,
    required this.type,
    required this.reward,
    required this.active,
    this.expiresAt,
    required this.created,
    required this.updated,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'],
      title: json['title'],
      type: json['type'],
      reward: (json['reward'] as num).toDouble(),
      active: json['active'] ?? true,
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'reward': reward,
      'active': active,
      'expiresAt': expiresAt?.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  String get formattedReward => '\$${reward.toStringAsFixed(2)}';
}

class Completion {
  final String id;
  final String userId;
  final String taskId;
  final DateTime completedAt;
  final DateTime created;
  final DateTime updated;

  Completion({
    required this.id,
    required this.userId,
    required this.taskId,
    required this.completedAt,
    required this.created,
    required this.updated,
  });

  factory Completion.fromJson(Map<String, dynamic> json) {
    return Completion(
      id: json['id'],
      userId: json['user'],
      taskId: json['task'],
      completedAt: DateTime.parse(json['completedAt']),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'task': taskId,
      'completedAt': completedAt.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }
}

class Reward {
  final String id;
  final String userId;
  final double amount;
  final String source;
  final DateTime createdAt;
  final DateTime created;
  final DateTime updated;

  Reward({
    required this.id,
    required this.userId,
    required this.amount,
    required this.source,
    required this.createdAt,
    required this.created,
    required this.updated,
  });

  factory Reward.fromJson(Map<String, dynamic> json) {
    return Reward(
      id: json['id'],
      userId: json['user'],
      amount: (json['amount'] as num).toDouble(),
      source: json['source'],
      createdAt: DateTime.parse(json['createdAt']),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'amount': amount,
      'source': source,
      'createdAt': createdAt.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';
}

class Withdrawal {
  final String id;
  final String userId;
  final double amount;
  final String status; // pending, paid, rejected
  final DateTime requestedAt;
  final DateTime created;
  final DateTime updated;

  Withdrawal({
    required this.id,
    required this.userId,
    required this.amount,
    required this.status,
    required this.requestedAt,
    required this.created,
    required this.updated,
  });

  factory Withdrawal.fromJson(Map<String, dynamic> json) {
    return Withdrawal(
      id: json['id'],
      userId: json['user'],
      amount: (json['amount'] as num).toDouble(),
      status: json['status'],
      requestedAt: DateTime.parse(json['requestedAt']),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'amount': amount,
      'status': status,
      'requestedAt': requestedAt.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';
  
  bool get isPending => status == 'pending';
  bool get isPaid => status == 'paid';
  bool get isRejected => status == 'rejected';
}
