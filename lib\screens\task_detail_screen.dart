import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/appodeal_service.dart';
import '../widgets/global_banner_ad.dart';
import 'dart:math';
import 'package:flutter/services.dart';
import 'dart:async';

class TaskDetailScreen extends StatefulWidget {
  final String taskId;
  final String title;
  final String difficulty;
  final String reward;
  final List<Color> colors;

  const TaskDetailScreen({
    super.key,
    required this.taskId,
    required this.title,
    required this.difficulty,
    required this.reward,
    required this.colors,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen> with TickerProviderStateMixin {
  late AnimationController _progressController;
  int currentLinkIndex = 0;
  int completedLinks = 0;
  
  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  // Sample checkpoints (in real app, these come from admin)
  final List<String> checkpoints = [
    'https://gplinks.in/abc123',
    'https://gplinks.in/def456',
    'https://gplinks.in/ghi789',
    'https://gplinks.in/jkl012',
    'https://gplinks.in/mno345',
  ];

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: babyBlue,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_rounded, color: navyBlue),
        ),
        title: Text(
          widget.taskId,
          style: const TextStyle(
            color: navyBlue,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Task Info Header
              _buildTaskHeader(),
              const SizedBox(height: 24),
              
              // Progress Indicator
              _buildProgressIndicator(),
              const SizedBox(height: 32),

              // Checkpoints List
              _buildCheckpointsList(),
              const SizedBox(height: 24),
              
              // Instructions
              _buildInstructions(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTaskHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: widget.colors),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: widget.colors[0].withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.link_rounded,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.difficulty.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: goldenYellow,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.reward,
                  style: const TextStyle(
                    color: navyBlue,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const Spacer(),
              const Text(
                '5 Checkpoints',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Progress',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: navyBlue,
              ),
            ),
            Text(
              '$completedLinks / 5 Checkpoints',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: widget.colors[0],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: List.generate(5, (index) {
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(right: index < 4 ? 8 : 0),
                height: 8,
                decoration: BoxDecoration(
                  gradient: index < completedLinks 
                    ? LinearGradient(colors: widget.colors)
                    : null,
                  color: index < completedLinks ? null : Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildCheckpointsList() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: navyBlue.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Task Checkpoints',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 20),
          ...List.generate(5, (index) {
            final isCompleted = index < completedLinks;
            final isCurrent = index == currentLinkIndex && !isCompleted;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isCompleted
                  ? mintGreen.withOpacity(0.1)
                  : isCurrent
                    ? widget.colors[0].withOpacity(0.1)
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isCompleted
                    ? mintGreen
                    : isCurrent
                      ? widget.colors[0]
                      : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                        ? mintGreen
                        : isCurrent
                          ? widget.colors[0]
                          : Colors.grey[400],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: isCompleted
                        ? const Icon(Icons.check, color: Colors.white, size: 18)
                        : Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Checkpoint ${index + 1}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isCurrent ? navyBlue : Colors.grey[600],
                      ),
                    ),
                  ),
                  if (isCurrent && !isCompleted)
                    ElevatedButton(
                      onPressed: () => _openLink(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.colors[0],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Start',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: widget.colors[0].withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                color: widget.colors[0],
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Instructions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: navyBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '1. Click Start to open checkpoint in browser\n'
            '2. Complete the ad/offer on the page\n'
            '3. Return to app and solve captcha\n'
            '4. Repeat for all 5 checkpoints\n'
            '5. Earn your reward!\n\n'
            '⚠️ Important: Do not use VPN or proxy\n'
            'You will be banned for using VPN/proxy',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }



  void _openLink() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(
          url: checkpoints[currentLinkIndex],
          onCompleted: _onLinkCompleted,
        ),
      ),
    );
  }

  void _onLinkCompleted() {
    setState(() {
      completedLinks++;
      if (currentLinkIndex < 4) {
        currentLinkIndex++;
      }
    });

    _progressController.forward().then((_) {
      _progressController.reset();
    });

    if (completedLinks == 5) {
      _showTaskCompletedDialog();
    }
  }

  void _showTaskCompletedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '🎉 Task Completed!',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: navyBlue,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Congratulations! You have completed all 5 checkpoints.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: widget.colors),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${widget.reward} added to Partial Wallet',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.colors[0],
              foregroundColor: Colors.white,
            ),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }
}

class WebViewScreen extends StatefulWidget {
  final String url;
  final VoidCallback onCompleted;

  const WebViewScreen({
    super.key,
    required this.url,
    required this.onCompleted,
  });

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late final WebViewController controller;
  bool _hasReachedDestination = false;
  bool _canGoBack = false;
  bool _isLoading = true;
  String _currentUrl = '';
  Timer? _progressTimer;

  // Example final destination URLs - In real app, these would come from the task data
  final Map<String, String> _finalDestinations = {
    'https://gplinks.in/abc123': 'youtube.com',
    'https://gplinks.in/def456': 'amazon.com',
    'https://gplinks.in/ghi789': 'flipkart.com',
    'https://gplinks.in/jkl012': 'myntra.com',
    'https://gplinks.in/mno345': 'paytm.com',
  };

  // External links that should open outside WebView
  final List<String> _externalDomains = [
    'telegram.me',
    't.me',
    'whatsapp.com',
    'wa.me',
    'instagram.com',
    'facebook.com',
    'twitter.com',
    'play.google.com',
    'apps.apple.com',
  ];

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    super.dispose();
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _currentUrl = url;
            });
            _updateBackButtonState();
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
              _currentUrl = url;
            });
            _updateBackButtonState();
            _checkIfDestinationReached(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            // Check if this is an external link that should open outside
            if (_shouldOpenExternally(request.url)) {
              _handleExternalLink(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView error: ${error.description}');
            _showErrorDialog(error.description);
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  Future<void> _updateBackButtonState() async {
    final canGoBack = await controller.canGoBack();
    if (mounted) {
      setState(() {
        _canGoBack = canGoBack;
      });
    }
  }

  bool _shouldOpenExternally(String url) {
    return _externalDomains.any((domain) => url.contains(domain));
  }

  void _handleExternalLink(String url) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          '🔗 External Link',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
        ),
        content: Text(
          'This link will open in your default browser:\n\n$url\n\nAfter completing the action, return to this app to continue.',
          style: const TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Here you would normally use url_launcher to open the link
              // For now, we'll simulate the action
              _simulateExternalLinkCompletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Open'),
          ),
        ],
      ),
    );
  }

  void _simulateExternalLinkCompletion() {
    // Simulate user returning from external app
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ External action completed! Continuing task...'),
            backgroundColor: Colors.green,
          ),
        );
        // Continue with the task flow
        _checkIfDestinationReached(_currentUrl);
      }
    });
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          '⚠️ Connection Error',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Colors.orange),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Unable to load the page. This might be due to:\n\n• Poor internet connection\n• Server issues\n• Ad blocker interference',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Text(
              'Error: $error',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to task screen
            },
            child: const Text('Go Back'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _retryLoading();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _retryLoading() {
    setState(() {
      _isLoading = true;
    });
    controller.reload();
  }

  void _checkIfDestinationReached(String currentUrl) {
    final expectedDomain = _finalDestinations[widget.url];
    if (expectedDomain != null && currentUrl.contains(expectedDomain)) {
      if (!_hasReachedDestination) {
        setState(() {
          _hasReachedDestination = true;
        });
        _showDestinationReachedDialog();
      }
    }
  }

  void _showDestinationReachedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          '🎉 Destination Reached!',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: Colors.green,
          ),
        ),
        content: const Text(
          'Great! You\'ve reached the final destination. Now complete the captcha to earn your reward.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close WebView
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CaptchaScreen(
                    onCompleted: widget.onCompleted,
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: const Text('Continue to Captcha'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Handle back button press
        if (_canGoBack) {
          await controller.goBack();
          return false; // Don't pop the screen
        }
        return true; // Allow pop if can't go back
      },
      child: Scaffold(
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Complete the offer',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              if (_currentUrl.isNotEmpty)
                Text(
                  Uri.parse(_currentUrl).host,
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
                ),
            ],
          ),
          backgroundColor: _hasReachedDestination ? Colors.green : Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              if (_canGoBack) {
                await controller.goBack();
              } else {
                Navigator.pop(context);
              }
            },
          ),
          actions: [
            // Back button for WebView
            if (_canGoBack)
              IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () async {
                  await controller.goBack();
                },
                tooltip: 'Go Back',
              ),

            // Forward button
            IconButton(
              icon: const Icon(Icons.arrow_forward_ios),
              onPressed: () async {
                final canGoForward = await controller.canGoForward();
                if (canGoForward) {
                  await controller.goForward();
                }
              },
              tooltip: 'Go Forward',
            ),

            // Refresh button
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _retryLoading,
              tooltip: 'Refresh',
            ),

            // Continue button
            if (_hasReachedDestination)
              IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CaptchaScreen(
                        onCompleted: widget.onCompleted,
                      ),
                    ),
                  );
                },
                tooltip: 'Continue to Captcha',
              ),
          ],
        ),
        body: Column(
          children: [
            // Loading indicator
            if (_isLoading)
              Container(
                width: double.infinity,
                height: 4,
                child: LinearProgressIndicator(
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _hasReachedDestination ? Colors.green : Colors.blue,
                  ),
                ),
              ),

            // Success banner
            if (_hasReachedDestination)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: Colors.green.shade100,
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade700),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '🎉 Destination reached! You can now proceed to captcha.',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CaptchaScreen(
                              onCompleted: widget.onCompleted,
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Continue'),
                    ),
                  ],
                ),
              ),

            // WebView
            Expanded(
              child: WebViewWidget(controller: controller),
            ),
          ],
        ),
      ),
    );
  }
}

class CaptchaScreen extends StatefulWidget {
  final VoidCallback onCompleted;

  const CaptchaScreen({
    super.key,
    required this.onCompleted,
  });

  @override
  State<CaptchaScreen> createState() => _CaptchaScreenState();
}

class _CaptchaScreenState extends State<CaptchaScreen> {
  final TextEditingController _captchaController = TextEditingController();
  final AppodealService _appodealService = AppodealService();
  bool _isProcessing = false;

  // Dynamic captcha variables
  late int _num1;
  late int _num2;
  late String _operation;
  late int _correctAnswer;
  late String _question;

  @override
  void initState() {
    super.initState();
    _generateCaptcha();
  }

  void _generateCaptcha() {
    final random = Random();
    _num1 = random.nextInt(20) + 1; // 1-20
    _num2 = random.nextInt(20) + 1; // 1-20

    final operations = ['+', '-', '×'];
    _operation = operations[random.nextInt(operations.length)];

    switch (_operation) {
      case '+':
        _correctAnswer = _num1 + _num2;
        break;
      case '-':
        // Ensure positive result
        if (_num1 < _num2) {
          final temp = _num1;
          _num1 = _num2;
          _num2 = temp;
        }
        _correctAnswer = _num1 - _num2;
        break;
      case '×':
        _num1 = random.nextInt(10) + 1; // Smaller numbers for multiplication
        _num2 = random.nextInt(10) + 1;
        _correctAnswer = _num1 * _num2;
        break;
    }

    _question = '$_num1 $_operation $_num2 = ?';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Completion'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Banner ad 1 (Top)
          const GlobalBannerAd(adId: 'captcha_top_banner', height: 60),

          Expanded(
            child: Row(
              children: [
                // Banner ad 3 (Left Vertical)
                const GlobalBannerAd(
                  adId: 'captcha_left_banner_1',
                  isVertical: true,
                  width: 70,
                ),

                // Banner ad 4 (Left Vertical 2)
                const GlobalBannerAd(
                  adId: 'captcha_left_banner_2',
                  isVertical: true,
                  width: 70,
                ),

                // Main Content Area
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(12),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.security_rounded,
                          size: 40,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          'Human Verification',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _question,
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.w700,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: 120,
                          child: TextField(
                            controller: _captchaController,
                            keyboardType: TextInputType.number,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Answer',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              contentPadding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isProcessing ? null : _handleSubmit,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade600,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: _isProcessing
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Verify',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Banner ad 6 (Right Vertical)
                const GlobalBannerAd(
                  adId: 'captcha_right_banner_1',
                  isVertical: true,
                  width: 70,
                ),

                // Banner ad 5 (Right Vertical 2)
                const GlobalBannerAd(
                  adId: 'captcha_right_banner_2',
                  isVertical: true,
                  width: 70,
                ),
              ],
            ),
          ),

          // Banner ad 2 (Bottom)
          const GlobalBannerAd(adId: 'captcha_bottom_banner', height: 60),
        ],
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (int.tryParse(_captchaController.text) == _correctAnswer) {
      setState(() {
        _isProcessing = true;
      });

      // Show rewarded video ad after correct captcha
      try {
        final adShown = await _appodealService.showRewardedVideo();
        if (adShown) {
          // Wait for ad to complete
          await Future.delayed(const Duration(seconds: 2));
        }
      } catch (e) {
        print('Failed to show rewarded video: $e');
      }

      // Complete the task
      Navigator.pop(context);
      widget.onCompleted();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Incorrect answer. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
      _captchaController.clear();
      // Generate new captcha on wrong answer
      _generateCaptcha();
      setState(() {});
    }
  }

  @override
  void dispose() {
    _captchaController.dispose();
    super.dispose();
  }
}
