import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/appodeal_service.dart';
import '../widgets/global_banner_ad.dart';
import 'dart:math';

class TaskDetailScreen extends StatefulWidget {
  final String taskId;
  final String title;
  final String difficulty;
  final String reward;
  final List<Color> colors;

  const TaskDetailScreen({
    super.key,
    required this.taskId,
    required this.title,
    required this.difficulty,
    required this.reward,
    required this.colors,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen> with TickerProviderStateMixin {
  late AnimationController _progressController;
  int currentLinkIndex = 0;
  int completedLinks = 0;
  
  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  // Sample checkpoints (in real app, these come from admin)
  final List<String> checkpoints = [
    'https://gplinks.in/abc123',
    'https://gplinks.in/def456',
    'https://gplinks.in/ghi789',
    'https://gplinks.in/jkl012',
    'https://gplinks.in/mno345',
  ];

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: babyBlue,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_rounded, color: navyBlue),
        ),
        title: Text(
          widget.taskId,
          style: const TextStyle(
            color: navyBlue,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Task Info Header
              _buildTaskHeader(),
              const SizedBox(height: 24),
              
              // Progress Indicator
              _buildProgressIndicator(),
              const SizedBox(height: 32),

              // Checkpoints List
              _buildCheckpointsList(),
              const SizedBox(height: 24),
              
              // Instructions
              _buildInstructions(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTaskHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: widget.colors),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: widget.colors[0].withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.link_rounded,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.difficulty.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: goldenYellow,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.reward,
                  style: const TextStyle(
                    color: navyBlue,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const Spacer(),
              const Text(
                '5 Checkpoints',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Progress',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: navyBlue,
              ),
            ),
            Text(
              '$completedLinks / 5 Checkpoints',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: widget.colors[0],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: List.generate(5, (index) {
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(right: index < 4 ? 8 : 0),
                height: 8,
                decoration: BoxDecoration(
                  gradient: index < completedLinks 
                    ? LinearGradient(colors: widget.colors)
                    : null,
                  color: index < completedLinks ? null : Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildCheckpointsList() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: navyBlue.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Task Checkpoints',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: navyBlue,
            ),
          ),
          const SizedBox(height: 20),
          ...List.generate(5, (index) {
            final isCompleted = index < completedLinks;
            final isCurrent = index == currentLinkIndex && !isCompleted;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isCompleted
                  ? mintGreen.withOpacity(0.1)
                  : isCurrent
                    ? widget.colors[0].withOpacity(0.1)
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isCompleted
                    ? mintGreen
                    : isCurrent
                      ? widget.colors[0]
                      : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                        ? mintGreen
                        : isCurrent
                          ? widget.colors[0]
                          : Colors.grey[400],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: isCompleted
                        ? const Icon(Icons.check, color: Colors.white, size: 18)
                        : Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Checkpoint ${index + 1}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isCompleted || isCurrent ? navyBlue : Colors.grey[600],
                      ),
                    ),
                  ),
                  if (isCurrent && !isCompleted)
                    ElevatedButton(
                      onPressed: () => _openLink(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.colors[0],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Start',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: widget.colors[0].withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                color: widget.colors[0],
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Instructions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: navyBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '1. Click Start to open checkpoint in browser\n'
            '2. Complete the ad/offer on the page\n'
            '3. Return to app and solve captcha\n'
            '4. Repeat for all 5 checkpoints\n'
            '5. Earn your reward!\n\n'
            '⚠️ Important: Do not use VPN or proxy\n'
            'You will be banned for using VPN/proxy',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }



  void _openLink() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(
          url: checkpoints[currentLinkIndex],
          onCompleted: _onLinkCompleted,
        ),
      ),
    );
  }

  void _onLinkCompleted() {
    setState(() {
      completedLinks++;
      if (currentLinkIndex < 4) {
        currentLinkIndex++;
      }
    });

    _progressController.forward().then((_) {
      _progressController.reset();
    });

    if (completedLinks == 5) {
      _showTaskCompletedDialog();
    }
  }

  void _showTaskCompletedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '🎉 Task Completed!',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: navyBlue,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Congratulations! You have completed all 5 checkpoints.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: widget.colors),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${widget.reward} added to Partial Wallet',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.colors[0],
              foregroundColor: Colors.white,
            ),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }
}

class WebViewScreen extends StatefulWidget {
  final String url;
  final VoidCallback onCompleted;

  const WebViewScreen({
    super.key,
    required this.url,
    required this.onCompleted,
  });

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late final WebViewController controller;

  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete the offer'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CaptchaScreen(
                    onCompleted: widget.onCompleted,
                  ),
                ),
              );
            },
            child: const Text(
              'Done',
              style: TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}

class CaptchaScreen extends StatefulWidget {
  final VoidCallback onCompleted;

  const CaptchaScreen({
    super.key,
    required this.onCompleted,
  });

  @override
  State<CaptchaScreen> createState() => _CaptchaScreenState();
}

class _CaptchaScreenState extends State<CaptchaScreen> {
  final TextEditingController _captchaController = TextEditingController();
  final AppodealService _appodealService = AppodealService();
  bool _isProcessing = false;

  // Dynamic captcha variables
  late int _num1;
  late int _num2;
  late String _operation;
  late int _correctAnswer;
  late String _question;

  @override
  void initState() {
    super.initState();
    _generateCaptcha();
  }

  void _generateCaptcha() {
    final random = Random();
    _num1 = random.nextInt(20) + 1; // 1-20
    _num2 = random.nextInt(20) + 1; // 1-20

    final operations = ['+', '-', '×'];
    _operation = operations[random.nextInt(operations.length)];

    switch (_operation) {
      case '+':
        _correctAnswer = _num1 + _num2;
        break;
      case '-':
        // Ensure positive result
        if (_num1 < _num2) {
          final temp = _num1;
          _num1 = _num2;
          _num2 = temp;
        }
        _correctAnswer = _num1 - _num2;
        break;
      case '×':
        _num1 = random.nextInt(10) + 1; // Smaller numbers for multiplication
        _num2 = random.nextInt(10) + 1;
        _correctAnswer = _num1 * _num2;
        break;
    }

    _question = '$_num1 $_operation $_num2 = ?';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Completion'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Banner ad 1 (Top)
          const GlobalBannerAd(adId: 'Banner ad 1', height: 60),

          Expanded(
            child: Row(
              children: [
                // Banner ad 3 (Left Vertical)
                const GlobalBannerAd(
                  adId: 'Banner ad 3',
                  isVertical: true,
                  width: 70,
                ),

                // Banner ad 4 (Left Vertical 2)
                const GlobalBannerAd(
                  adId: 'Banner ad 4',
                  isVertical: true,
                  width: 70,
                ),

                // Main Content Area
                Expanded(
                  child: Column(
                    children: [
                      // Top Row Banners
                      Row(
                        children: [
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 7', height: 50)),
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 8', height: 50)),
                        ],
                      ),

                      // Main Captcha Content (Compact)
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.all(8),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.security_rounded,
                                size: 28,
                                color: Colors.blue,
                              ),
                              const SizedBox(height: 6),
                              const Text(
                                'Human Verification',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.blue,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  _question,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              SizedBox(
                                width: 100,
                                child: TextField(
                                  controller: _captchaController,
                                  keyboardType: TextInputType.number,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'Answer',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(vertical: 6),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: _isProcessing ? null : _handleSubmit,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue.shade600,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: _isProcessing
                                    ? const SizedBox(
                                        height: 16,
                                        width: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : const Text(
                                        'Verify',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Bottom Row Banners 1
                      Row(
                        children: [
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 9', height: 50)),
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 10', height: 50)),
                        ],
                      ),

                      // Bottom Row Banners 2
                      Row(
                        children: [
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 11', height: 50)),
                          Expanded(child: GlobalBannerAd(adId: 'Banner ad 12', height: 50)),
                        ],
                      ),
                    ],
                  ),
                ),

                // Banner ad 6 (Right Vertical)
                const GlobalBannerAd(
                  adId: 'Banner ad 6',
                  isVertical: true,
                  width: 70,
                ),

                // Banner ad 5 (Right Vertical 2)
                const GlobalBannerAd(
                  adId: 'Banner ad 5',
                  isVertical: true,
                  width: 70,
                ),
              ],
            ),
          ),

          // Banner ad 2 (Bottom)
          const GlobalBannerAd(adId: 'Banner ad 2', height: 60),
        ],
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (int.tryParse(_captchaController.text) == _correctAnswer) {
      setState(() {
        _isProcessing = true;
      });

      // Show rewarded video ad after correct captcha
      try {
        final adShown = await _appodealService.showRewardedVideo();
        if (adShown) {
          // Wait for ad to complete
          await Future.delayed(const Duration(seconds: 2));
        }
      } catch (e) {
        print('Failed to show rewarded video: $e');
      }

      // Complete the task
      Navigator.pop(context);
      widget.onCompleted();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Incorrect answer. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
      _captchaController.clear();
      // Generate new captcha on wrong answer
      _generateCaptcha();
      setState(() {});
    }
  }

  @override
  void dispose() {
    _captchaController.dispose();
    super.dispose();
  }
}
