<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Tic <PERSON>c <PERSON> - Game Launcher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .launcher-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .game-logo {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .game-tagline {
            font-size: 20px;
            margin-bottom: 30px;
            color: #FFD700;
            font-style: italic;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #FFD700;
        }

        .feature-desc {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .difficulty-showcase {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .difficulty-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .difficulty-desc {
            font-size: 16px;
            opacity: 0.9;
        }

        .play-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 24px;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.4);
            text-transform: uppercase;
            letter-spacing: 2px;
            margin: 20px 10px;
        }

        .play-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(76, 175, 80, 0.6);
        }

        .secondary-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
            margin: 10px;
        }

        .secondary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
        }

        .game-rules {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
        }

        .rules-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
            text-align: center;
        }

        .rules-list {
            list-style: none;
            padding: 0;
        }

        .rules-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .rules-list li:last-child {
            border-bottom: none;
        }

        .rule-icon {
            margin-right: 10px;
            font-size: 18px;
        }

        .stats-preview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }

        @media (max-width: 600px) {
            .launcher-container {
                padding: 20px;
                margin: 20px;
            }
            
            .game-logo {
                font-size: 36px;
            }
            
            .play-button {
                padding: 15px 30px;
                font-size: 18px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <h1 class="game-logo">🎯 Ultimate Tic Tac Toe</h1>
        <p class="game-tagline">"Never Draw Edition - The Impossible Challenge"</p>
        
        <div class="difficulty-showcase">
            <div class="difficulty-title">🔥 IMPOSSIBLE AI MODE</div>
            <div class="difficulty-desc">Face an unbeatable AI that uses advanced minimax algorithm with strategic positioning!</div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">♻️</div>
                <div class="feature-title">Never Draw System</div>
                <div class="feature-desc">When 6+ moves are made, oldest moves disappear automatically, preventing draws and keeping the game dynamic!</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <div class="feature-title">Impossible AI</div>
                <div class="feature-desc">Advanced minimax algorithm with strategic positioning makes this AI extremely challenging to beat!</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">✨</div>
                <div class="feature-title">Beautiful UI</div>
                <div class="feature-desc">Stunning animations, smooth transitions, and modern design create an amazing gaming experience!</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Mobile Ready</div>
                <div class="feature-desc">Fully responsive design works perfectly on all devices with touch controls and optimized layout!</div>
            </div>
        </div>
        
        <div class="stats-preview">
            <div class="stat-item">
                <div class="stat-number">∞</div>
                <div class="stat-label">Replayability</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">0%</div>
                <div class="stat-label">Draw Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">Challenge</div>
            </div>
        </div>
        
        <button class="play-button" onclick="startGame()">
            🎮 Start Challenge
        </button>
        
        <div class="game-rules">
            <div class="rules-title">🎯 How to Play</div>
            <ul class="rules-list">
                <li><span class="rule-icon">❌</span> You are X, AI is O</li>
                <li><span class="rule-icon">🎯</span> Get 3 in a row to win (horizontal, vertical, diagonal)</li>
                <li><span class="rule-icon">🛡️</span> Block AI from getting 3 in a row</li>
                <li><span class="rule-icon">♻️</span> After 6 moves, oldest moves disappear</li>
                <li><span class="rule-icon">🔥</span> No draws possible - fight until someone wins!</li>
                <li><span class="rule-icon">🏆</span> Beat the impossible AI to earn rewards!</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px;">
            <button class="secondary-button" onclick="showDemo()">📺 Watch Demo</button>
            <button class="secondary-button" onclick="showStrategy()">🧠 Strategy Tips</button>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }
        
        function showDemo() {
            alert(`🎮 DEMO PREVIEW:

🎯 Game Flow:
1. Click any cell to place your X
2. AI immediately responds with O
3. Continue until someone gets 3 in a row
4. When board fills up, oldest moves vanish
5. Game continues until there's a winner!

✨ Special Features:
• Smooth animations for placing/removing pieces
• AI thinking indicator
• Move history tracking
• Beautiful visual effects
• No draw situations possible!

Ready to try? Click "Start Challenge"!`);
        }
        
        function showStrategy() {
            alert(`🧠 STRATEGY TIPS vs IMPOSSIBLE AI:

🎯 Opening Strategy:
• Start with center (position 4) if possible
• Corners are strong second choices
• Avoid edges unless necessary

🛡️ Defensive Play:
• Always block AI's winning moves
• Watch for fork opportunities (2 ways to win)
• Control the center when possible

♻️ Late Game (when pieces disappear):
• Remember which pieces will vanish next
• Plan moves considering disappearing pieces
• Use disappearing mechanics to your advantage

🔥 Advanced Tips:
• AI is nearly unbeatable, but not impossible
• Look for patterns in AI's play style
• Stay focused - one mistake and AI wins!

Good luck beating the impossible! 🍀`);
        }
    </script>
</body>
</html>
