import 'package:flutter/material.dart';

class UserManagement extends StatefulWidget {
  const UserManagement({super.key});

  @override
  State<UserManagement> createState() => _UserManagementState();
}

class _UserManagementState extends State<UserManagement> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedFilter = 'all';
  
  // Admin Color Scheme
  static const Color primaryBlue = Color(0xFF1565C0);
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFF44336);

  // Mock user data
  final List<Map<String, dynamic>> _users = [
    {
      'id': '1',
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'joinDate': '2024-01-15',
      'lastActive': '2 hours ago',
      'totalEarnings': 145.50,
      'tasksCompleted': 23,
      'status': 'active',
      'deviceId': 'DEV001',
      'banned': false,
    },
    {
      'id': '2',
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'joinDate': '2024-01-20',
      'lastActive': 'Online',
      'totalEarnings': 89.20,
      'tasksCompleted': 15,
      'status': 'online',
      'deviceId': 'DEV002',
      'banned': false,
    },
    {
      'id': '3',
      'name': 'Mike Wilson',
      'email': '<EMAIL>',
      'joinDate': '2024-01-10',
      'lastActive': '1 day ago',
      'totalEarnings': 234.80,
      'tasksCompleted': 45,
      'status': 'inactive',
      'deviceId': 'DEV003',
      'banned': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with Stats
        _buildUserStats(),
        const SizedBox(height: 24),
        
        // Search and Filters
        _buildSearchAndFilters(),
        const SizedBox(height: 24),
        
        // Users Table
        Expanded(
          child: _buildUsersTable(),
        ),
      ],
    );
  }

  Widget _buildUserStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total Users',
            value: '2,543',
            icon: Icons.people,
            color: primaryBlue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Online Now',
            value: '127',
            icon: Icons.circle,
            color: successGreen,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Banned Users',
            value: '15',
            icon: Icons.block,
            color: errorRed,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'New Today',
            value: '23',
            icon: Icons.person_add,
            color: accentOrange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF1565C0),
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Field
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users by name, email, or device ID...',
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade500),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: primaryBlue),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 16),
          
          // Filter Dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButton<String>(
              value: _selectedFilter,
              underline: const SizedBox(),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('All Users')),
                DropdownMenuItem(value: 'online', child: Text('Online')),
                DropdownMenuItem(value: 'active', child: Text('Active')),
                DropdownMenuItem(value: 'inactive', child: Text('Inactive')),
                DropdownMenuItem(value: 'banned', child: Text('Banned')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
              },
            ),
          ),
          const SizedBox(width: 16),
          
          // Export Button
          ElevatedButton.icon(
            onPressed: () {
              // Export users data
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: accentOrange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: const Icon(Icons.download),
            label: const Text('Export'),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(flex: 2, child: Text('User', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Status', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Earnings', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Tasks', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Last Active', style: TextStyle(fontWeight: FontWeight.w600))),
                const Expanded(child: Text('Actions', style: TextStyle(fontWeight: FontWeight.w600))),
              ],
            ),
          ),
          
          // Table Body
          Expanded(
            child: ListView.builder(
              itemCount: _users.length,
              itemBuilder: (context, index) {
                final user = _users[index];
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    children: [
                      // User Info
                      Expanded(
                        flex: 2,
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 20,
                              backgroundColor: primaryBlue,
                              child: Text(
                                user['name'][0],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    user['name'],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    user['email'],
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Status
                      Expanded(
                        child: _buildStatusChip(user['status'], user['banned']),
                      ),
                      
                      // Earnings
                      Expanded(
                        child: Text(
                          '\$${user['totalEarnings'].toStringAsFixed(2)}',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: successGreen,
                          ),
                        ),
                      ),
                      
                      // Tasks
                      Expanded(
                        child: Text(
                          user['tasksCompleted'].toString(),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      
                      // Last Active
                      Expanded(
                        child: Text(
                          user['lastActive'],
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      
                      // Actions
                      Expanded(
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: () => _viewUserDetails(user),
                              icon: const Icon(Icons.visibility),
                              color: primaryBlue,
                              tooltip: 'View Details',
                            ),
                            IconButton(
                              onPressed: () => _toggleUserBan(user),
                              icon: Icon(user['banned'] ? Icons.check_circle : Icons.block),
                              color: user['banned'] ? successGreen : errorRed,
                              tooltip: user['banned'] ? 'Unban User' : 'Ban User',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status, bool banned) {
    Color color;
    String text;
    
    if (banned) {
      color = errorRed;
      text = 'Banned';
    } else {
      switch (status) {
        case 'online':
          color = successGreen;
          text = 'Online';
          break;
        case 'active':
          color = primaryBlue;
          text = 'Active';
          break;
        case 'inactive':
          color = Colors.grey;
          text = 'Inactive';
          break;
        default:
          color = Colors.grey;
          text = 'Unknown';
      }
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _viewUserDetails(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('User Details: ${user['name']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: ${user['email']}'),
            Text('Device ID: ${user['deviceId']}'),
            Text('Join Date: ${user['joinDate']}'),
            Text('Total Earnings: \$${user['totalEarnings']}'),
            Text('Tasks Completed: ${user['tasksCompleted']}'),
            Text('Status: ${user['banned'] ? 'Banned' : user['status']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _toggleUserBan(Map<String, dynamic> user) {
    setState(() {
      user['banned'] = !user['banned'];
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          user['banned'] 
              ? 'User ${user['name']} has been banned'
              : 'User ${user['name']} has been unbanned',
        ),
        backgroundColor: user['banned'] ? errorRed : successGreen,
      ),
    );
  }
}
