@echo off
echo ========================================
echo Easy Money - PocketBase Setup Script
echo ========================================
echo.

:: Create backend directory
echo Creating backend directory...
if not exist "easy_money_backend" mkdir easy_money_backend
cd easy_money_backend

:: Extract PocketBase from assets
echo Extracting PocketBase from assets...
powershell -command "Expand-Archive -Path '../assets/pocketbase_0.28.3_windows_amd64.zip' -DestinationPath '.' -Force"

:: Check if extraction was successful
if exist "pocketbase.exe" (
    echo PocketBase extracted successfully!
) else (
    echo Error: PocketBase extraction failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo PocketBase Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Run: pocketbase.exe serve
echo 2. Open browser: http://127.0.0.1:8090/_/
echo 3. Create admin account
echo 4. Create collections as per setup guide
echo.
echo Starting PocketBase server...
echo Press Ctrl+C to stop the server
echo.

:: Start PocketBase server
pocketbase.exe serve

pause
