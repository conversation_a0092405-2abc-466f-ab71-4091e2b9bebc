import 'package:flutter/material.dart';
import 'widgets/admin_sidebar.dart';
import 'widgets/admin_header.dart';
import 'pages/dashboard_overview.dart';
import 'pages/user_management.dart';
import 'pages/task_management.dart';
import 'pages/wallet_management.dart';
import 'pages/analytics_page.dart';
import 'pages/security_tools.dart';
import 'pages/admin_tools.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;
  bool _isSidebarCollapsed = false;

  // Admin Color Scheme
  static const Color primaryBlue = Color(0xFF1565C0);
  static const Color darkBlue = Color(0xFF0D47A1);
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFF44336);

  final List<AdminPage> _pages = [
    AdminPage(
      title: 'Dashboard',
      icon: Icons.dashboard,
      widget: const DashboardOverview(),
    ),
    AdminPage(
      title: 'User Management',
      icon: Icons.people,
      widget: const UserManagement(),
    ),
    AdminPage(
      title: 'Task Management',
      icon: Icons.assignment,
      widget: const TaskManagement(),
    ),
    AdminPage(
      title: 'Wallet & Withdrawals',
      icon: Icons.account_balance_wallet,
      widget: const WalletManagement(),
    ),
    AdminPage(
      title: 'Analytics & Revenue',
      icon: Icons.analytics,
      widget: const AnalyticsPage(),
    ),
    AdminPage(
      title: 'Security & Fraud',
      icon: Icons.security,
      widget: const SecurityTools(),
    ),
    AdminPage(
      title: 'Admin Tools',
      icon: Icons.admin_panel_settings,
      widget: const AdminTools(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Row(
        children: [
          // Sidebar
          AdminSidebar(
            selectedIndex: _selectedIndex,
            onItemSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            isCollapsed: _isSidebarCollapsed,
            onToggleCollapse: () {
              setState(() {
                _isSidebarCollapsed = !_isSidebarCollapsed;
              });
            },
            pages: _pages,
          ),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Header
                AdminHeader(
                  title: _pages[_selectedIndex].title,
                  onMenuPressed: () {
                    setState(() {
                      _isSidebarCollapsed = !_isSidebarCollapsed;
                    });
                  },
                ),
                
                // Page Content
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    child: _pages[_selectedIndex].widget,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class AdminPage {
  final String title;
  final IconData icon;
  final Widget widget;

  AdminPage({
    required this.title,
    required this.icon,
    required this.widget,
  });
}
