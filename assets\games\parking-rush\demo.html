<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parking <PERSON> Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .car-demo {
            font-size: 48px;
            margin: 20px 0;
            animation: carMove 2s ease-in-out infinite;
        }
        @keyframes carMove {
            0%, 100% { transform: translateX(-20px); }
            50% { transform: translateX(20px); }
        }
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        button {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .win-btn {
            background: #4CAF50;
            color: white;
        }
        .lose-btn {
            background: #ff6b6b;
            color: white;
        }
        button:hover {
            transform: scale(1.05);
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🚗 Parking Rush</h1>
        <div class="car-demo">🚗💨</div>
        <p>Draw paths for cars to reach their parking spots!</p>
        
        <div class="buttons">
            <button class="win-btn" onclick="sendWin()">
                🎉 Win Game
            </button>
            <button class="lose-btn" onclick="sendLose()">
                💥 Lose Game
            </button>
        </div>
        
        <div class="info">
            <p>This is a demo of the Parking Rush game integration.</p>
            <p>Click the buttons to test win/lose events.</p>
        </div>
    </div>

    <script>
        function sendWin() {
            console.log('Sending win event');
            if (window.parent && window.parent.postMessage) {
                window.parent.postMessage('win', '*');
            }
            if (window.gameEvents && window.gameEvents.postMessage) {
                window.gameEvents.postMessage('win');
            }
            alert('Win event sent! 🎉');
        }

        function sendLose() {
            console.log('Sending lose event');
            if (window.parent && window.parent.postMessage) {
                window.parent.postMessage('lose', '*');
            }
            if (window.gameEvents && window.gameEvents.postMessage) {
                window.gameEvents.postMessage('lose');
            }
            alert('Lose event sent! 💥');
        }

        // Auto-send a test message after 3 seconds
        setTimeout(() => {
            console.log('Auto-sending test message');
            if (window.gameEvents && window.gameEvents.postMessage) {
                window.gameEvents.postMessage('Game loaded successfully!');
            }
        }, 3000);
    </script>
</body>
</html>
