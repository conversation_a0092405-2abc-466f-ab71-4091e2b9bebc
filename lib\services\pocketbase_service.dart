import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class PocketBaseService {
  // Use your local IP address when testing on real devices
  // For emulator, use 127.0.0.1 or ********
  final String baseUrl = 'http://127.0.0.1:8090';
  
  String? _token;
  String? _userId;
  Map<String, dynamic>? _currentUser;

  // Initialize service and load stored auth data
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString('token');
    _userId = prefs.getString('userId');
    final userDataString = prefs.getString('userData');
    if (userDataString != null) {
      _currentUser = jsonDecode(userDataString);
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => _token != null && _userId != null;

  // Get current user ID
  String? get currentUserId => _userId;

  // Get current user data
  Map<String, dynamic>? get currentUser => _currentUser;

  // Login with email and password
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final url = Uri.parse('$baseUrl/api/collections/users/auth-with-password');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'identity': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _token = data['token'];
        _userId = data['record']['id'];
        _currentUser = data['record'];

        // Store auth data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', _token!);
        await prefs.setString('userId', _userId!);
        await prefs.setString('userData', jsonEncode(_currentUser!));

        return {'success': true, 'user': data['record']};
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['message'] ?? 'Login failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Register new user
  Future<Map<String, dynamic>> register(String email, String password, String name) async {
    try {
      final url = Uri.parse('$baseUrl/api/collections/users/records');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'passwordConfirm': password,
          'name': name,
        }),
      );

      if (response.statusCode == 200) {
        // Auto-login after successful registration
        return await login(email, password);
      } else {
        final error = jsonDecode(response.body);
        return {'success': false, 'error': error['message'] ?? 'Registration failed'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Logout user
  Future<void> logout() async {
    _token = null;
    _userId = null;
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    await prefs.remove('userId');
    await prefs.remove('userData');
  }

  // Fetch available tasks
  Future<List<Map<String, dynamic>>> fetchTasks() async {
    try {
      final url = Uri.parse('$baseUrl/api/collections/tasks/records?filter=active=true');
      final response = await http.get(
        url,
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['items']);
      }
      return [];
    } catch (e) {
      print('Error fetching tasks: $e');
      return [];
    }
  }

  // Complete a task
  Future<Map<String, dynamic>> completeTask(String taskId, double reward) async {
    try {
      // First, create completion record
      final completionUrl = Uri.parse('$baseUrl/api/collections/completions/records');
      final completionResponse = await http.post(
        completionUrl,
        headers: _getAuthHeaders(),
        body: jsonEncode({
          'user': _userId,
          'task': taskId,
          'completedAt': DateTime.now().toIso8601String(),
        }),
      );

      if (completionResponse.statusCode == 200) {
        // Then, add reward record
        final rewardUrl = Uri.parse('$baseUrl/api/collections/rewards/records');
        final rewardResponse = await http.post(
          rewardUrl,
          headers: _getAuthHeaders(),
          body: jsonEncode({
            'user': _userId,
            'amount': reward,
            'source': 'task_completion',
            'createdAt': DateTime.now().toIso8601String(),
          }),
        );

        if (rewardResponse.statusCode == 200) {
          return {'success': true, 'reward': reward};
        }
      }
      
      return {'success': false, 'error': 'Failed to complete task'};
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Get user's total balance
  Future<double> getUserBalance() async {
    try {
      final url = Uri.parse('$baseUrl/api/collections/rewards/records?filter=user="$_userId"');
      final response = await http.get(
        url,
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final rewards = List<Map<String, dynamic>>.from(data['items']);
        
        double total = 0.0;
        for (var reward in rewards) {
          total += (reward['amount'] as num).toDouble();
        }
        
        return total;
      }
      return 0.0;
    } catch (e) {
      print('Error fetching balance: $e');
      return 0.0;
    }
  }

  // Request withdrawal
  Future<Map<String, dynamic>> requestWithdrawal(double amount) async {
    try {
      final url = Uri.parse('$baseUrl/api/collections/withdrawals/records');
      final response = await http.post(
        url,
        headers: _getAuthHeaders(),
        body: jsonEncode({
          'user': _userId,
          'amount': amount,
          'status': 'pending',
          'requestedAt': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        return {'success': true, 'message': 'Withdrawal request submitted'};
      } else {
        return {'success': false, 'error': 'Failed to submit withdrawal request'};
      }
    } catch (e) {
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Helper method to get auth headers
  Map<String, String> _getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      if (_token != null) 'Authorization': 'Bearer $_token',
    };
  }
}

// Singleton instance
final pocketBaseService = PocketBaseService();
