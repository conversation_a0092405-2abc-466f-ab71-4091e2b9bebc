import 'dart:async';
import 'dart:developer';
import 'package:stack_appodeal_flutter/stack_appodeal_flutter.dart';

class AppodealService {
  static final AppodealService _instance = AppodealService._internal();
  factory AppodealService() => _instance;
  AppodealService._internal();

  // Your Appodeal App ID
  static const String _appKey = 'fbcc85986084ac5f24921a8856198d2171247fe418b0b247';
  
  bool _isInitialized = false;
  bool _isRewardedVideoLoaded = false;
  bool _isInterstitialLoaded = false;
  bool _isBannerLoaded = false;

  // Callbacks for ad events
  Function(double)? onRewardedVideoCompleted;
  Function()? onRewardedVideoFailed;
  Function()? onInterstitialClosed;
  Function()? onBannerLoaded;
  Function()? onBannerFailed;

  /// Initialize Appodeal with all ad types
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('🚀 Initializing Appodeal with App ID: $_appKey');

      // Set up callbacks before initialization
      _setupCallbacks();

      // Initialize Appodeal with all ad types
      await Appodeal.initialize(
        appKey: _appKey,
        adTypes: [
          AppodealAdType.RewardedVideo,
          AppodealAdType.Interstitial,
          AppodealAdType.Banner,
        ],
      );

      // Set user settings for better ad targeting
      await _setUserSettings();

      _isInitialized = true;
      log('✅ Appodeal initialized successfully');

      // Preload ads
      await _preloadAds();

    } catch (e) {
      log('❌ Failed to initialize Appodeal: $e');
    }
  }

  /// Set up ad event callbacks
  void _setupCallbacks() {
    // Rewarded Video Callbacks
    Appodeal.setRewardedVideoCallbacks(
      onRewardedVideoLoaded: (isPrecache) {
        _isRewardedVideoLoaded = true;
        log('📺 Rewarded video loaded (precache: $isPrecache)');
      },
      onRewardedVideoFailedToLoad: () {
        _isRewardedVideoLoaded = false;
        log('❌ Rewarded video failed to load');
        onRewardedVideoFailed?.call();
      },
      onRewardedVideoShown: () {
        log('📺 Rewarded video shown');
      },
      onRewardedVideoFinished: (amount, currency) {
        log('🎉 Rewarded video completed! Reward: $amount $currency');
        onRewardedVideoCompleted?.call(amount);
        _isRewardedVideoLoaded = false;
        // Preload next rewarded video
        _preloadRewardedVideo();
      },
      onRewardedVideoClosed: (isFinished) {
        log('📺 Rewarded video closed (finished: $isFinished)');
        if (!isFinished) {
          onRewardedVideoFailed?.call();
        }
      },
    );

    // Interstitial Callbacks
    Appodeal.setInterstitialCallbacks(
      onInterstitialLoaded: (isPrecache) {
        _isInterstitialLoaded = true;
        log('📱 Interstitial loaded (precache: $isPrecache)');
      },
      onInterstitialFailedToLoad: () {
        _isInterstitialLoaded = false;
        log('❌ Interstitial failed to load');
      },
      onInterstitialShown: () {
        log('📱 Interstitial shown');
      },
      onInterstitialClosed: () {
        log('📱 Interstitial closed');
        _isInterstitialLoaded = false;
        onInterstitialClosed?.call();
        // Preload next interstitial
        _preloadInterstitial();
      },
    );

    // Banner Callbacks
    Appodeal.setBannerCallbacks(
      onBannerLoaded: (isPrecache) {
        _isBannerLoaded = true;
        log('📰 Banner loaded (precache: $isPrecache)');
        onBannerLoaded?.call();
      },
      onBannerFailedToLoad: () {
        _isBannerLoaded = false;
        log('❌ Banner failed to load');
        onBannerFailed?.call();
      },
      onBannerShown: () {
        log('📰 Banner shown');
      },
      onBannerClicked: () {
        log('📰 Banner clicked');
      },
    );
  }

  /// Set user settings for better ad targeting
  Future<void> _setUserSettings() async {
    try {
      // Enable auto cache for better performance
      await Appodeal.setAutoCache(AppodealAdType.RewardedVideo, true);
      await Appodeal.setAutoCache(AppodealAdType.Interstitial, true);

      log('⚙️ User settings configured');
    } catch (e) {
      log('⚠️ Failed to set user settings: $e');
    }
  }

  /// Preload all ad types
  Future<void> _preloadAds() async {
    await _preloadRewardedVideo();
    await _preloadInterstitial();
  }

  /// Preload rewarded video
  Future<void> _preloadRewardedVideo() async {
    try {
      await Appodeal.cache(AppodealAdType.RewardedVideo);
      log('🔄 Preloading rewarded video...');
    } catch (e) {
      log('❌ Failed to preload rewarded video: $e');
    }
  }

  /// Preload interstitial
  Future<void> _preloadInterstitial() async {
    try {
      await Appodeal.cache(AppodealAdType.Interstitial);
      log('🔄 Preloading interstitial...');
    } catch (e) {
      log('❌ Failed to preload interstitial: $e');
    }
  }

  /// Show rewarded video ad
  Future<bool> showRewardedVideo() async {
    if (!_isInitialized) {
      log('⚠️ Appodeal not initialized');
      return false;
    }

    try {
      final isLoaded = await Appodeal.isLoaded(AppodealAdType.RewardedVideo);
      if (isLoaded) {
        await Appodeal.show(AppodealAdType.RewardedVideo);
        return true;
      } else {
        log('⚠️ Rewarded video not loaded');
        // Try to load and show
        await _preloadRewardedVideo();
        return false;
      }
    } catch (e) {
      log('❌ Failed to show rewarded video: $e');
      return false;
    }
  }

  /// Show interstitial ad
  Future<bool> showInterstitial() async {
    if (!_isInitialized) {
      log('⚠️ Appodeal not initialized');
      return false;
    }

    try {
      final isLoaded = await Appodeal.isLoaded(AppodealAdType.Interstitial);
      if (isLoaded) {
        await Appodeal.show(AppodealAdType.Interstitial);
        return true;
      } else {
        log('⚠️ Interstitial not loaded');
        // Try to load and show
        await _preloadInterstitial();
        return false;
      }
    } catch (e) {
      log('❌ Failed to show interstitial: $e');
      return false;
    }
  }

  /// Show banner ad
  Future<bool> showBanner() async {
    if (!_isInitialized) {
      log('⚠️ Appodeal not initialized');
      return false;
    }

    try {
      await Appodeal.show(AppodealAdType.BannerBottom);
      return true;
    } catch (e) {
      log('❌ Failed to show banner: $e');
      return false;
    }
  }

  /// Hide banner ad
  Future<void> hideBanner() async {
    try {
      await Appodeal.hide(AppodealAdType.BannerBottom);
      log('📰 Banner hidden');
    } catch (e) {
      log('❌ Failed to hide banner: $e');
    }
  }

  /// Check if rewarded video is available
  Future<bool> isRewardedVideoAvailable() async {
    if (!_isInitialized) return false;
    try {
      return await Appodeal.isLoaded(AppodealAdType.RewardedVideo);
    } catch (e) {
      return false;
    }
  }

  /// Check if interstitial is available
  Future<bool> isInterstitialAvailable() async {
    if (!_isInitialized) return false;
    try {
      return await Appodeal.isLoaded(AppodealAdType.Interstitial);
    } catch (e) {
      return false;
    }
  }

  /// Get current ad availability status
  Map<String, bool> getAdStatus() {
    return {
      'initialized': _isInitialized,
      'rewardedVideo': _isRewardedVideoLoaded,
      'interstitial': _isInterstitialLoaded,
      'banner': _isBannerLoaded,
    };
  }

  /// Dispose resources
  void dispose() {
    onRewardedVideoCompleted = null;
    onRewardedVideoFailed = null;
    onInterstitialClosed = null;
    onBannerLoaded = null;
    onBannerFailed = null;
  }
}
