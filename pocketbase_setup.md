# PocketBase Setup for Easy Money App

## 1. Download and Setup PocketBase

1. Extract `pocketbase_0.28.3_windows_amd64.zip` from assets folder
2. Create a new folder: `easy_money_backend`
3. Copy `pocketbase.exe` to this folder
4. Open terminal in this folder and run: `./pocketbase.exe serve`
5. Open browser and go to: `http://127.0.0.1:8090/_/`
6. Create admin account (email/password)

## 2. Create Collections

### Users Collection (already exists, just configure)
- **Fields:**
  - `name` (Text)
  - `email` (Email) 
  - `avatar` (File)
  - `verified` (Bool)
  - `banned` (Bool, default: false)
  - `device_id` (Text)
  - `last_active` (DateTime)
  - `total_earnings` (Number, default: 0)
  - `wallet_balance` (Number, default: 0)

### Tasks Collection
- **Fields:**
  - `title` (Text, required)
  - `description` (Text)
  - `type` (Select: shortlink, video, game)
  - `difficulty` (Select: easy, medium, hard)
  - `reward_amount` (Number, required)
  - `original_reward` (Number)
  - `completion_goal` (Number, default: 1000)
  - `current_completions` (Number, default: 0)
  - `links` (JSON) // Array of short links
  - `active` (Bool, default: true)
  - `expires_at` (DateTime)

### Completions Collection
- **Fields:**
  - `user` (Relation to users)
  - `task` (Relation to tasks)
  - `completed_at` (DateTime)
  - `device_id` (Text)
  - `ip_address` (Text)
  - `completion_time` (Number) // seconds taken
  - `captcha_attempts` (Number, default: 1)

### Rewards Collection
- **Fields:**
  - `user` (Relation to users)
  - `task` (Relation to tasks, optional)
  - `amount` (Number, required)
  - `source` (Select: task_completion, bonus, referral)
  - `status` (Select: pending, approved, paid)
  - `created_at` (DateTime)

### Withdrawals Collection
- **Fields:**
  - `user` (Relation to users)
  - `amount` (Number, required)
  - `payment_method` (Select: paypal, bank, upi)
  - `payment_details` (JSON)
  - `status` (Select: pending, processing, completed, rejected)
  - `requested_at` (DateTime)
  - `processed_at` (DateTime)
  - `admin_notes` (Text)

### Analytics Collection
- **Fields:**
  - `date` (Date)
  - `total_users` (Number, default: 0)
  - `active_users` (Number, default: 0)
  - `tasks_completed` (Number, default: 0)
  - `ads_played` (Number, default: 0)
  - `revenue_generated` (Number, default: 0)
  - `withdrawals_processed` (Number, default: 0)

### Admin_Messages Collection
- **Fields:**
  - `title` (Text, required)
  - `message` (Text, required)
  - `type` (Select: info, warning, success, error)
  - `target_users` (Select: all, specific, banned)
  - `active` (Bool, default: true)
  - `created_by` (Text) // admin email
  - `expires_at` (DateTime)

## 3. Security Rules

### Users Collection Rules:
```javascript
// List/Search Rule
@request.auth.id != ""

// View Rule  
@request.auth.id = id || @request.auth.role = "admin"

// Create Rule
@request.auth.id = ""

// Update Rule
@request.auth.id = id || @request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Tasks Collection Rules:
```javascript
// List/Search Rule
active = true || @request.auth.role = "admin"

// View Rule
active = true || @request.auth.role = "admin"

// Create Rule
@request.auth.role = "admin"

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Completions Collection Rules:
```javascript
// List/Search Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// View Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// Create Rule
@request.auth.id = user.id

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Rewards Collection Rules:
```javascript
// List/Search Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// View Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// Create Rule
@request.auth.role = "admin"

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Withdrawals Collection Rules:
```javascript
// List/Search Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// View Rule
@request.auth.id = user.id || @request.auth.role = "admin"

// Create Rule
@request.auth.id = user.id

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Analytics Collection Rules:
```javascript
// List/Search Rule
@request.auth.role = "admin"

// View Rule
@request.auth.role = "admin"

// Create Rule
@request.auth.role = "admin"

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

### Admin_Messages Collection Rules:
```javascript
// List/Search Rule
active = true || @request.auth.role = "admin"

// View Rule
active = true || @request.auth.role = "admin"

// Create Rule
@request.auth.role = "admin"

// Update Rule
@request.auth.role = "admin"

// Delete Rule
@request.auth.role = "admin"
```

## 4. Update App Configuration

Update `lib/services/pocketbase_service.dart`:
- Change `baseUrl` from `http://127.0.0.1:8090` to your server IP
- For local testing: keep `http://127.0.0.1:8090`
- For production: use your domain/IP

## 5. Admin Panel Access

1. Go to: `http://127.0.0.1:8090/_/`
2. Login with admin credentials
3. Access all collections and data
4. Use the custom admin panel (to be created) for advanced features

## 6. Next Steps

1. Run PocketBase server
2. Create collections with above schema
3. Set security rules
4. Test with Flutter app
5. Deploy admin panel for advanced management
