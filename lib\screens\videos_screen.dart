import 'package:flutter/material.dart';
import '../services/appodeal_service.dart';
import '../services/global_ad_manager.dart';
import '../widgets/global_banner_ad.dart';

class VideosScreen extends StatefulWidget {
  const VideosScreen({super.key});

  @override
  State<VideosScreen> createState() => _VideosScreenState();
}

class _VideosScreenState extends State<VideosScreen> with TickerProviderStateMixin, GlobalAdMixin {
  late AnimationController _fadeController;
  final AppodealService _appodealService = AppodealService();
  bool _isAdLoading = false;

  // Expanded Summer Splash + Modern Color Palette
  static const Color navyBlue = Color(0xFF05445E);
  static const Color blueGrotto = Color(0xFF189AB4);
  static const Color blueGreen = Color(0xFF75E6DA);
  static const Color babyBlue = Color(0xFFD4F1F4);
  static const Color coral = Color(0xFFFF6B6B);
  static const Color sunsetOrange = Color(0xFFFF8E53);
  static const Color goldenYellow = Color(0xFFFFD93D);
  static const Color mintGreen = Color(0xFF6BCF7F);
  static const Color lavender = Color(0xFFA8E6CF);
  static const Color roseGold = Color(0xFFF7CAC9);

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController.forward();

    // Set up Appodeal callbacks
    _setupAppodealCallbacks();
  }

  void _setupAppodealCallbacks() {
    _appodealService.onRewardedVideoCompleted = (reward) {
      _showAdCompletionDialog(reward);
    };

    _appodealService.onRewardedVideoFailed = () {
      _showAdFailedDialog();
    };
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _appodealService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              babyBlue,
              Color(0xFFE8F4FD),
            ],
          ),
        ),
        child: SafeArea(
          child: OrientationBuilder(
            builder: (context, orientation) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    // Header
                    _buildHeader(),

                    // Watch Ads Section
                    _buildWatchAdsSection(),

                    // Promotional Videos Section
                    _buildPromotionalVideos(),
                  ],
                ),
              );
            },
          ),
        ),
      ),
      bottomNavigationBar: const GlobalBannerAd(
        adId: 'Videos Banner',
        height: 60,
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📺 Watch & Earn',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w900,
                        color: navyBlue,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Watch ads & promotional content to earn',
                      style: TextStyle(
                        fontSize: 12,
                        color: navyBlue.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.all(14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [coral, sunsetOrange],
                  ),
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: [
                    BoxShadow(
                      color: coral.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.play_circle_filled_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWatchAdsSection() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [coral, sunsetOrange]),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.ads_click_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Watch Ads',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w900,
                  color: navyBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Single Watch Ad Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [coral, sunsetOrange],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: coral.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled_rounded,
                    color: Colors.white,
                    size: 48,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Watch Ad & Earn',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Watch ads and earn rewards',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: FutureBuilder<bool>(
                    future: _appodealService.isRewardedVideoAvailable(),
                    builder: (context, snapshot) {
                      final isAvailable = snapshot.data ?? false;
                      return ElevatedButton(
                        onPressed: _isAdLoading ? null : () {
                          if (isAvailable) {
                            _watchRewardedVideo();
                          } else {
                            _showAdNotAvailableDialog();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isAvailable ? Colors.white : Colors.grey.shade300,
                          foregroundColor: isAvailable ? coral : Colors.grey.shade600,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 0,
                        ),
                        child: _isAdLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(
                              isAvailable ? 'Watch Ad Now' : 'Ad Loading...',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _watchRewardedVideo() async {
    setState(() {
      _isAdLoading = true;
    });

    try {
      final success = await _appodealService.showRewardedVideo();
      if (!success) {
        _showAdNotAvailableDialog();
      }
    } catch (e) {
      _showAdFailedDialog();
    } finally {
      setState(() {
        _isAdLoading = false;
      });
    }
  }

  void _showAdNotAvailableDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '⚠️ Ad Not Available',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: sunsetOrange,
          ),
        ),
        content: const Text(
          'No ads are currently available. Please try again in a few moments.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: sunsetOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAdFailedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '❌ Ad Failed',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: coral,
          ),
        ),
        content: const Text(
          'The ad failed to load or was not completed. Please try again.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: coral,
              foregroundColor: Colors.white,
            ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  void _showAdCompletionDialog(double reward) {
    // Convert Appodeal reward to rupees (assuming 1 unit = ₹2-10)
    final rewardAmount = (reward * (2 + (DateTime.now().millisecond % 9))).toStringAsFixed(0);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '🎉 Ad Completed!',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: mintGreen,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(colors: [mintGreen, Color(0xFF4CAF50)]),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.monetization_on_rounded,
                    color: Colors.white,
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '₹$rewardAmount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                  const Text(
                    'added to Partial Wallet',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: mintGreen, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Keep watching ads to reach withdrawal goals!',
                      style: TextStyle(
                        fontSize: 12,
                        color: mintGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    foregroundColor: Colors.grey.shade700,
                  ),
                  child: const Text('Continue'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Watch another ad
                    Future.delayed(const Duration(milliseconds: 500), () {
                      _watchRewardedVideo();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: mintGreen,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Watch Another'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalVideos() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [blueGrotto, blueGreen]),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.video_collection_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Promotional Videos',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w900,
                  color: navyBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Admin-posted promotional content',
            style: TextStyle(
              fontSize: 14,
              color: navyBlue.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 20),

          // Promotional Videos List
          _buildPromotionalVideoCard(
            'New Product Launch',
            'Watch the latest smartphone launch event and earn rewards',
            '₹8.00',
            '4:30',
            '🚀',
            [coral, sunsetOrange],
          ),
          const SizedBox(height: 16),
          _buildPromotionalVideoCard(
            'Brand Partnership',
            'Exclusive brand collaboration announcement video',
            '₹6.00',
            '2:45',
            '🤝',
            [mintGreen, lavender],
          ),
          const SizedBox(height: 16),
          _buildPromotionalVideoCard(
            'App Tutorial',
            'Learn new features and maximize your earnings',
            '₹5.00',
            '3:15',
            '📱',
            [goldenYellow, sunsetOrange],
          ),
          const SizedBox(height: 16),
          _buildPromotionalVideoCard(
            'Success Stories',
            'Hear from top earners and their strategies',
            '₹7.00',
            '5:20',
            '⭐',
            [lavender, roseGold],
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalVideoCard(
    String title,
    String description,
    String reward,
    String duration,
    String emoji,
    List<Color> colors,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Video Thumbnail
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: colors),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      emoji,
                      style: const TextStyle(fontSize: 32),
                    ),
                  ),
                  Positioned(
                    bottom: 4,
                    right: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        duration,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // Video Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: navyBlue,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: navyBlue.withOpacity(0.7),
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: colors),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          reward,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Playing: $title'),
                              backgroundColor: colors[0],
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colors[0],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Watch',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
