# 🚗 Parking Rush - HTML5 Game

A mobile-friendly puzzle game where players draw paths for colored cars to reach their matching parking spots without colliding.

## 🎮 Game Features

### Core Gameplay
- **Path Drawing**: Draw lines from cars to matching colored parking spots
- **Collision Detection**: Cars crash if they get too close (30px threshold)
- **Multiple Levels**: 3 challenging levels with increasing difficulty
- **Win/Lose Events**: Triggers `window.postMessage('win'/'lose')` for Flutter integration

### Technical Features
- **HTML5 Canvas**: Smooth rendering and animations
- **Touch Support**: Works on mobile devices with touch controls
- **Responsive Design**: Adapts to different screen sizes
- **60fps Animation**: Uses `requestAnimationFrame` for smooth movement
- **Clean Code**: Modular, well-commented JavaScript

## 📁 File Structure

```
parking-rush/
├── index.html          # Main game page
├── style.css           # Game styling
├── game.js             # Game logic
├── test.html           # Test page with instructions
├── assets/             # Game assets
│   ├── car.svg         # Car SVG template
│   └── README.md       # Assets documentation
└── README.md           # This file
```

## 🎯 How to Play

1. **Draw Paths**: Click/touch and drag from each car to its matching colored parking spot
2. **Plan Routes**: Make sure paths don't cause cars to collide
3. **Start Animation**: Click "Start Cars" when all paths are drawn
4. **Watch & Win**: Cars follow your paths - complete without crashes to win!

## 🔧 Integration with Flutter

The game sends messages to the parent window for reward ads:

```javascript
// On win
window.postMessage('win');

// On lose (collision)
window.postMessage('lose');
```

## 🎨 Game Levels

### Level 1: Basic (2 cars)
- Red car → Red parking spot
- Blue car → Blue parking spot

### Level 2: Crossing (3 cars)
- Cars must cross paths strategically
- Red, Green, Blue cars

### Level 3: Complex (4 cars)
- Maximum challenge with 4 cars
- Red, Green, Blue, Yellow cars

## 🚀 Usage in Flutter WebView

```dart
WebView(
  initialUrl: 'file:///path/to/parking-rush/index.html',
  javascriptMode: JavascriptMode.unrestricted,
  onWebViewCreated: (WebViewController webViewController) {
    // Handle game events
  },
  javascriptChannels: <JavascriptChannel>{
    JavascriptChannel(
      name: 'gameEvents',
      onMessageReceived: (JavascriptMessage message) {
        if (message.message == 'win') {
          // Show reward ad
        } else if (message.message == 'lose') {
          // Show retry option
        }
      },
    ),
  },
)
```

## 🎮 Controls

- **Mouse**: Click and drag to draw paths
- **Touch**: Touch and drag on mobile devices
- **Buttons**: 
  - 🚗 Start Cars: Begin animation
  - 🗑️ Clear Paths: Remove all drawn paths
  - 🔄 Restart: Restart current level

## 🏆 Scoring

- Base score: 100 points per level
- Multiplier: Level number × 100
- Total score accumulates across levels

## 🔄 Game States

1. **Menu**: Initial loading state
2. **Playing**: Drawing paths
3. **Animating**: Cars moving along paths
4. **Won**: Level completed successfully
5. **Lost**: Cars collided

## 📱 Mobile Optimization

- Touch-friendly controls
- Responsive canvas sizing
- Optimized for portrait and landscape
- Smooth touch drawing
- No zoom/scroll interference

## 🎨 Customization

The game can be easily customized:

- **Colors**: Modify car and parking spot colors
- **Levels**: Add new levels in the `levels` array
- **Speed**: Adjust car movement speed
- **Collision**: Change collision detection radius
- **UI**: Modify styling in `style.css`

## 🐛 Browser Compatibility

- ✅ Chrome (Mobile & Desktop)
- ✅ Safari (Mobile & Desktop)
- ✅ Firefox (Mobile & Desktop)
- ✅ Edge (Mobile & Desktop)

## 📄 License

This game is created for the Easy Money Flutter app. All rights reserved.
