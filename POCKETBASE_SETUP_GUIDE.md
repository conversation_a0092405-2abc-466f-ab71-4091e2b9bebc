# 🗄️ **PocketBase Setup Guide for Easy Money App**

## ✅ **Step 1: Create Admin Account**
1. **Open browser:** `http://127.0.0.1:8090/_/`
2. **Create your admin account** with email and password
3. **Login to PocketBase dashboard**

---

## 📊 **Step 2: Create Collections**

### **Collection 1: tasks**
```
Name: tasks
Type: Base

Fields:
- title (Text, Required, Min: 3, Max: 100)
- description (Text, Optional, Max: 500)
- type (Select, Required, Values: shortlink, video, game)
- difficulty (Select, Required, Values: easy, medium, hard)
- reward_amount (Number, Required, Min: 0.01, Max: 100)
- original_reward (Number, Required, Min: 0.01, Max: 100)
- completion_goal (Number, Required, Min: 1, Max: 10000)
- current_completions (Number, Optional, Min: 0, Default: 0)
- links (JSON, Optional)
- active (Bool, Optional, Default: true)
- expires_at (Date, Optional)

API Rules:
- List: active = true || @request.auth.role = 'admin'
- View: active = true || @request.auth.role = 'admin'
- Create: @request.auth.role = 'admin'
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

### **Collection 2: completions**
```
Name: completions
Type: Base

Fields:
- user (Relation to users, Required, Cascade Delete)
- task (Relation to tasks, Required, Cascade Delete)
- completed_at (Date, Required)
- device_id (Text, Required)
- ip_address (Text, Required)
- completion_time (Number, Optional, Min: 0)
- captcha_attempts (Number, Optional, Min: 1, Max: 10)

API Rules:
- List: @request.auth.id = user.id || @request.auth.role = 'admin'
- View: @request.auth.id = user.id || @request.auth.role = 'admin'
- Create: @request.auth.id = user.id
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

### **Collection 3: rewards**
```
Name: rewards
Type: Base

Fields:
- user (Relation to users, Required, Cascade Delete)
- task (Relation to tasks, Optional)
- amount (Number, Required, Min: 0.01)
- source (Select, Required, Values: task_completion, bonus, referral)
- status (Select, Required, Values: pending, approved, paid)

API Rules:
- List: @request.auth.id = user.id || @request.auth.role = 'admin'
- View: @request.auth.id = user.id || @request.auth.role = 'admin'
- Create: @request.auth.role = 'admin'
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

### **Collection 4: withdrawals**
```
Name: withdrawals
Type: Base

Fields:
- user (Relation to users, Required, Cascade Delete)
- amount (Number, Required, Min: 1)
- payment_method (Select, Required, Values: paypal, bank, upi)
- payment_details (JSON, Required)
- status (Select, Required, Values: pending, processing, completed, rejected)
- requested_at (Date, Required)
- processed_at (Date, Optional)
- admin_notes (Text, Optional)

API Rules:
- List: @request.auth.id = user.id || @request.auth.role = 'admin'
- View: @request.auth.id = user.id || @request.auth.role = 'admin'
- Create: @request.auth.id = user.id
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

### **Collection 5: analytics**
```
Name: analytics
Type: Base

Fields:
- date (Date, Required)
- total_users (Number, Optional, Min: 0)
- active_users (Number, Optional, Min: 0)
- tasks_completed (Number, Optional, Min: 0)
- ads_played (Number, Optional, Min: 0)
- revenue_generated (Number, Optional, Min: 0)
- withdrawals_processed (Number, Optional, Min: 0)

API Rules:
- List: @request.auth.role = 'admin'
- View: @request.auth.role = 'admin'
- Create: @request.auth.role = 'admin'
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

### **Collection 6: admin_messages**
```
Name: admin_messages
Type: Base

Fields:
- title (Text, Required, Min: 3, Max: 100)
- message (Text, Required, Min: 10, Max: 1000)
- type (Select, Required, Values: info, warning, success, error)
- target_users (Select, Required, Values: all, specific, banned)
- active (Bool, Optional, Default: true)
- created_by (Text, Required)
- expires_at (Date, Optional)

API Rules:
- List: active = true || @request.auth.role = 'admin'
- View: active = true || @request.auth.role = 'admin'
- Create: @request.auth.role = 'admin'
- Update: @request.auth.role = 'admin'
- Delete: @request.auth.role = 'admin'
```

---

## 👥 **Step 3: Update Users Collection**

Go to **Settings > Auth collections > users** and add these fields:

```
Additional Fields:
- banned (Bool, Optional, Default: false)
- device_id (Text, Optional)
- last_active (Date, Optional)
- total_earnings (Number, Optional, Min: 0, Default: 0)
- wallet_balance (Number, Optional, Min: 0, Default: 0)
- role (Select, Optional, Values: user, admin, Default: user)
```

---

## 🔐 **Step 4: Create First Admin User**

1. Go to **Auth collections > users**
2. **Create new record** with:
   - Email: `<EMAIL>`
   - Password: `admin123456`
   - Role: `admin`
   - Verified: `true`

---

## ⚙️ **Step 5: Update App Configuration**

Edit `lib/services/pocketbase_service.dart`:

```dart
class PocketBaseService {
  static const String baseUrl = 'http://127.0.0.1:8090'; // For local testing
  // For production: 'http://your-domain.com:8090'
  
  // ... rest of the code
}
```

---

## 🧪 **Step 6: Test Complete Flow**

1. **Launch app** - Should show stunning login screen
2. **Register new user** - Creates account in PocketBase
3. **Login existing user** - Authenticates with backend
4. **Access admin panel** - Login with admin credentials
5. **Manage users/tasks** - Full admin functionality

---

## 🎯 **Sample Data for Testing**

### **Create Sample Tasks:**

**Task 1:**
```json
{
  "title": "Complete Tech Offers Challenge",
  "description": "Visit 5 technology websites and complete offers",
  "type": "shortlink",
  "difficulty": "easy",
  "reward_amount": 2.50,
  "original_reward": 5.00,
  "completion_goal": 1000,
  "current_completions": 756,
  "active": true,
  "links": [
    "https://gplinks.in/example1",
    "https://gplinks.in/example2",
    "https://gplinks.in/example3",
    "https://gplinks.in/example4",
    "https://gplinks.in/example5"
  ]
}
```

**Task 2:**
```json
{
  "title": "Watch Video Advertisements",
  "description": "Watch promotional videos to earn rewards",
  "type": "video",
  "difficulty": "easy",
  "reward_amount": 1.00,
  "original_reward": 1.00,
  "completion_goal": 500,
  "current_completions": 423,
  "active": true
}
```

**Task 3:**
```json
{
  "title": "Gaming Achievement Tasks",
  "description": "Complete gaming challenges and achievements",
  "type": "game",
  "difficulty": "hard",
  "reward_amount": 3.00,
  "original_reward": 3.00,
  "completion_goal": 200,
  "current_completions": 89,
  "active": true
}
```

---

## ✅ **Verification Checklist**

- [ ] PocketBase server running on `http://127.0.0.1:8090`
- [ ] Admin account created and accessible
- [ ] All 6 collections created with correct fields
- [ ] API rules configured properly
- [ ] Users collection updated with additional fields
- [ ] First admin user created
- [ ] App configuration updated
- [ ] Sample tasks created for testing
- [ ] Authentication flow working
- [ ] Admin panel accessible

---

## 🚀 **Ready for Production!**

Your **Easy Money** app is now fully configured with:
- ✅ Stunning login/signup screens
- ✅ Complete PocketBase backend
- ✅ Admin panel functionality
- ✅ Real-time data synchronization
- ✅ Secure authentication system

**Start earning! 💰**
